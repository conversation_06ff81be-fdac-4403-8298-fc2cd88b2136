# Nginx Reverse Proxy Configuration

This directory contains the nginx configuration for the Abra API Gateway.

## Overview

The nginx reverse proxy provides:

- **Single entry point** for all API services
- **Load balancing** and request routing
- **Rate limiting** to prevent abuse
- **Security headers** for enhanced security
- **Gzip compression** for better performance
- **Request logging** for monitoring

## Service Routing

All services are accessible through port 80 (HTTP) with the following routes:

### Assistant Service
- `GET/POST /api/assistant/*` → assistant-service:8080
- `GET/POST /api/chat/*` → assistant-service:8080

### Auth Service  
- `GET/POST /api/auth/*` → auth-service:8080

### Market Data Service
- `GET/POST /api/marketdata/*` → marketdata-service:8080
- `GET/POST /api/brokers/*` → marketdata-service:8080
- `GET/POST /api/watchlist/*` → marketdata-service:8080

### Special Endpoints
- `GET /health` → nginx health check
- `GET /` → API information
- `GET /swagger/*` → Service documentation (with ?service= parameter)

## Rate Limiting

- **API endpoints**: 10 requests/second per IP with burst of 20-50
- **<PERSON>urst handling**: Allows temporary spikes in traffic
- **Zone**: 10MB memory allocation for rate limiting

## Security Features

- **X-Frame-Options**: Prevents clickjacking
- **X-Content-Type-Options**: Prevents MIME sniffing
- **X-XSS-Protection**: Enables XSS filtering
- **Referrer-Policy**: Controls referrer information

## Configuration Files

- `nginx.conf` - Main nginx configuration
- Logs stored in `/var/log/nginx/` (mapped to docker volume)

## Usage Examples

```bash
# Health check
curl http://your-domain/health

# API information
curl http://your-domain/

# Assistant service
curl -X POST http://your-domain/api/assistant/ask \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Hello"}'

# Market data
curl http://your-domain/api/marketdata/AAPL

# Auth service
curl -X POST http://your-domain/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

## Monitoring

Access logs are available in the nginx_logs docker volume:
```bash
docker exec nginx-proxy tail -f /var/log/nginx/access.log
docker exec nginx-proxy tail -f /var/log/nginx/error.log
```
