events {
    worker_connections 1024;
}

http {
    upstream assistant-service {
        server assistant-service:8080;
    }
    
    upstream auth-service {
        server auth-service:8080;
    }
    
    upstream marketdata-service {
        server marketdata-service:8080;
    }
    
    upstream thread-service {
        server thread-service:8080;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # HTTP server - redirect to HTTPS
    server {
        listen 80;
        server_name abraapp.undeclab.com;

        # Allow Let's Encrypt challenges
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }

        # Redirect all other traffic to HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # HTTPS server
    server {
        listen 443 ssl http2;
        server_name abraapp.undeclab.com;

        # SSL Configuration
        ssl_certificate /etc/letsencrypt/live/abraapp.undeclab.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/abraapp.undeclab.com/privkey.pem;

        # SSL Security Settings
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # HSTS (HTTP Strict Transport Security)
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Assistant Service routes
        location /api/assistant {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://assistant-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Timeout settings for AI responses
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;
        }

        location /api/chat {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://assistant-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Support for streaming responses
            proxy_buffering off;
            proxy_cache off;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;
        }

        # Auth Service routes
        location /api/auth {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://auth-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # User Service routes (also handled by auth-service)
        location /api/user {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://auth-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Market Data Service routes
        location /api/marketdata {
            limit_req zone=api burst=50 nodelay;
            proxy_pass http://marketdata-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/brokers {
            limit_req zone=api burst=50 nodelay;
            proxy_pass http://marketdata-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/watchlist {
            limit_req zone=api burst=50 nodelay;
            proxy_pass http://marketdata-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/symbols {
            limit_req zone=api burst=50 nodelay;
            proxy_pass http://marketdata-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/prices {
            limit_req zone=api burst=50 nodelay;
            proxy_pass http://marketdata-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/cache {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://marketdata-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Thread Service routes
        location /api/posts {
            limit_req zone=api burst=30 nodelay;
            proxy_pass http://thread-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/comments {
            limit_req zone=api burst=30 nodelay;
            proxy_pass http://thread-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/likes {
            limit_req zone=api burst=30 nodelay;
            proxy_pass http://thread-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Swagger documentation (development only)
        location /swagger/ {
            # Route to appropriate service based on path
            if ($arg_service = "assistant") {
                proxy_pass http://assistant-service;
            }
            if ($arg_service = "auth") {
                proxy_pass http://auth-service;
            }
            if ($arg_service = "marketdata") {
                proxy_pass http://marketdata-service;
            }
            if ($arg_service = "thread") {
                proxy_pass http://thread-service;
            }
            
            # Default to marketdata service
            proxy_pass http://marketdata-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Default route - return API information
        location / {
            return 200 '{"message":"Abra API Gateway","services":["assistant","auth","marketdata","thread"],"version":"1.0"}';
            add_header Content-Type application/json;
        }

        # Handle 404s
        location @404 {
            return 404 '{"error":"Endpoint not found","message":"Check API documentation"}';
            add_header Content-Type application/json;
        }
    }
}
