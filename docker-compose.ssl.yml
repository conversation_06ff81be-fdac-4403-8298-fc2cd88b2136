version: '3.8'

services:
  # Nginx with SSL support
  nginx:
    image: nginx:alpine
    container_name: abraapi-nginx-ssl
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./certbot/www:/var/www/certbot:ro
      - ./certbot/conf:/etc/letsencrypt:ro
    depends_on:
      - marketdata-service
      - auth-service
      - assistant-service
      - thread-service
    restart: unless-stopped
    networks:
      - abraapi-network

  # Certbot for SSL certificates
  certbot:
    image: certbot/certbot:latest
    container_name: abraapi-certbot
    volumes:
      - ./certbot/www:/var/www/certbot:rw
      - ./certbot/conf:/etc/letsencrypt:rw
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d abraapp.undeclab.com
    depends_on:
      - nginx

  # Market Data Service
  marketdata-service:
    build:
      context: ./MarketDataService
      dockerfile: Dockerfile
    container_name: abraapi-marketdata-ssl
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=${SUPABASE_CONNECTION_STRING}
      - BrokerSettings__FinnhubApiKey=${FINNHUB_API_KEY}
      - BrokerSettings__PolygonApiKey=${POLYGON_API_KEY}
      - Redis__ConnectionString=${REDIS_CONNECTION_STRING}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
    ports:
      - "5001:8080"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - abraapi-network

  # Auth Service
  auth-service:
    build:
      context: ./AuthService
      dockerfile: Dockerfile
    container_name: abraapi-auth-ssl
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=${SUPABASE_CONNECTION_STRING}
      - Supabase__Url=${SUPABASE_URL}
      - Supabase__Key=${SUPABASE_ANON_KEY}
    ports:
      - "5002:8080"
    restart: unless-stopped
    networks:
      - abraapi-network

  # Assistant Service
  assistant-service:
    build:
      context: ./AssistantService
      dockerfile: Dockerfile
    container_name: abraapi-assistant-ssl
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - Ollama__BaseUrl=${OLLAMA_BASE_URL}
    ports:
      - "5003:8080"
    restart: unless-stopped
    networks:
      - abraapi-network

  # Thread Service
  thread-service:
    build:
      context: ./ThreadService
      dockerfile: Dockerfile
    container_name: abraapi-thread-ssl
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=${SUPABASE_CONNECTION_STRING}
      - Supabase__Url=${SUPABASE_URL}
      - Supabase__Key=${SUPABASE_ANON_KEY}
    ports:
      - "5004:8080"
    restart: unless-stopped
    networks:
      - abraapi-network

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: abraapi-redis-ssl
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - abraapi-network

networks:
  abraapi-network:
    driver: bridge

volumes:
  redis_data:
