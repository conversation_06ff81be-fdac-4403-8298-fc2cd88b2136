# Use BuildKit for better caching and multi-platform builds
# syntax=docker/dockerfile:1

# Build stage with optimized caching
FROM mcr.microsoft.com/dotnet/sdk:10.0-preview AS build
WORKDIR /src

# Optimize .NET build performance
ENV DOTNET_CLI_TELEMETRY_OPTOUT=1
ENV DOTNET_SKIP_FIRST_TIME_EXPERIENCE=1
ENV NUGET_XMLDOC_MODE=skip
ENV DOTNET_SYSTEM_NET_HTTP_USESOCKETSHTTPHANDLER=false
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# Create NuGet cache directory
RUN mkdir -p /root/.nuget/packages

# Copy only project file first for better layer caching
COPY ["AuthService.csproj", "."]

# Restore packages - simplified for faster builds
RUN dotnet restore "AuthService.csproj" \
        --verbosity minimal

# Copy source code (separate layer for better caching)
COPY . .

# Build and publish with optimizations
RUN dotnet publish "AuthService.csproj" \
    -c Release \
    -o /app \
    --no-restore \
    --verbosity minimal

# Runtime stage - minimal Alpine image for smaller size
FROM mcr.microsoft.com/dotnet/aspnet:10.0-preview-alpine AS runtime
WORKDIR /app

# Install curl for health checks and create non-root user
RUN apk add --no-cache curl && \
    addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Copy published app with proper ownership
COPY --from=build --chown=appuser:appgroup /app .

# Switch to non-root user for security
USER appuser

# Expose port
EXPOSE 8080

# Add health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

ENTRYPOINT ["dotnet", "AuthService.dll"]