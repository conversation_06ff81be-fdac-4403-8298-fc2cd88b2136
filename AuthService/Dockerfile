# Use BuildKit for better caching and multi-platform builds
# syntax=docker/dockerfile:1

# Build stage with optimized caching for .NET 10
FROM mcr.microsoft.com/dotnet/sdk:10.0-preview AS build
WORKDIR /src

# Optimize .NET 10 build performance and enable new features
ENV DOTNET_CLI_TELEMETRY_OPTOUT=1
ENV DOTNET_SKIP_FIRST_TIME_EXPERIENCE=1
ENV NUGET_XMLDOC_MODE=skip
ENV DOTNET_SYSTEM_NET_HTTP_USESOCKETSHTTPHANDLER=false
ENV DOTNET_USE_POLLING_FILE_WATCHER=true
ENV DOTNET_EnableWriteXorExecute=0
ENV DOTNET_TieredPGO=1
ENV DOTNET_TC_QuickJitForLoops=1
ENV DOTNET_ReadyToRun=0

# Create optimized NuGet cache directory with proper permissions
RUN mkdir -p /root/.nuget/packages && \
    mkdir -p /tmp/nuget-scratch

# Copy only project file first for better layer caching
COPY ["AuthService.csproj", "."]

# Restore packages with .NET 10 optimizations
RUN dotnet restore "AuthService.csproj" \
        --verbosity minimal \
        --runtime linux-musl-x64 \
        --packages /root/.nuget/packages

# Copy source code (separate layer for better caching)
COPY . .

# Build and publish with .NET 10 optimizations and parallel processing
RUN dotnet publish "AuthService.csproj" \
    -c Release \
    -o /app \
    --no-restore \
    --verbosity minimal \
    --runtime linux-musl-x64 \
    --self-contained false \
    -p:PublishReadyToRun=false \
    -p:PublishSingleFile=false \
    -p:PublishTrimmed=false \
    -p:UseAppHost=false \
    -p:DebugType=None \
    -p:DebugSymbols=false \
    --maxcpucount

# Runtime stage - optimized Alpine image for .NET 10
FROM mcr.microsoft.com/dotnet/aspnet:10.0-preview-alpine AS runtime
WORKDIR /app

# Set .NET 10 runtime optimizations
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1
ENV DOTNET_EnableDiagnostics=0
ENV DOTNET_TieredPGO=1
ENV DOTNET_TC_QuickJitForLoops=1
ENV DOTNET_ReadyToRun=0
ENV ASPNETCORE_URLS=http://+:8080

# Install essential packages and create non-root user in single layer
RUN apk add --no-cache \
        curl \
        ca-certificates \
        tzdata && \
    addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup && \
    mkdir -p /app/logs && \
    chown -R appuser:appgroup /app

# Copy published app with proper ownership
COPY --from=build --chown=appuser:appgroup /app .

# Switch to non-root user for security
USER appuser

# Expose port
EXPOSE 8080

# Add optimized health check for .NET 10
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Use optimized entry point for .NET 10
ENTRYPOINT ["dotnet", "AuthService.dll"]