# Build artifacts
bin/
obj/
out/

# IDE files
.vs/
.vscode/
*.user
*.suo
*.userosscache
*.sln.docstates

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation
README.md
*.md

# Test files
*Tests/
*Test/
*.Tests/
*.Test/

# Logs
logs/
*.log

# Runtime files
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Docker files (except the one being used)
Dockerfile*
.dockerignore

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Temporary files
tmp/
temp/