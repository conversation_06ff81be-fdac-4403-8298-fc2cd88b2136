using System.Text.Json.Serialization;

namespace auth_service.Models;

// Supabase User Model
public class SupabaseUser
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    [JsonPropertyName("aud")]
    public string Aud { get; set; } = string.Empty;
    [JsonPropertyName("role")]
    public string Role { get; set; } = string.Empty;
    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;
    [JsonPropertyName("phone")]
    public string? Phone { get; set; }
    [JsonPropertyName("email_confirmed_at")]
    public DateTime? EmailConfirmedAt { get; set; }
    [JsonPropertyName("phone_confirmed_at")]
    public DateTime? PhoneConfirmedAt { get; set; }
    [JsonPropertyName("confirmation_sent_at")]
    public DateTime? ConfirmationSentAt { get; set; }
    [JsonPropertyName("confirmed_at")]
    public DateTime? ConfirmedAt { get; set; }
    [JsonPropertyName("recovery_sent_at")]
    public DateTime? RecoverySentAt { get; set; }
    [JsonPropertyName("email_change_sent_at")]
    public DateTime? EmailChangeSentAt { get; set; }
    [JsonPropertyName("new_email")]
    public string? NewEmail { get; set; }
    [JsonPropertyName("phone_change_sent_at")]
    public DateTime? PhoneChangeSentAt { get; set; }
    [JsonPropertyName("new_phone")]
    public string? NewPhone { get; set; }
    [JsonPropertyName("invited_at")]
    public DateTime? InvitedAt { get; set; }
    [JsonPropertyName("last_sign_in_at")]
    public DateTime? LastSignInAt { get; set; }
    [JsonPropertyName("banned_until")]
    public DateTime? BannedUntil { get; set; }
    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }
    [JsonPropertyName("updated_at")]
    public DateTime UpdatedAt { get; set; }
    [JsonPropertyName("user_metadata")]
    public Dictionary<string, object>? UserMetadata { get; set; }
    [JsonPropertyName("app_metadata")]
    public Dictionary<string, object>? AppMetadata { get; set; }
    [JsonPropertyName("identities")]
    public List<SupabaseIdentity>? Identities { get; set; }
}

public class SupabaseIdentity
{
    public string Id { get; set; } = string.Empty;
    [JsonPropertyName("user_id")]
    public string UserId { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    [JsonPropertyName("identity_data")]
    public Dictionary<string, object>? IdentityData { get; set; }
    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }
    [JsonPropertyName("updated_at")]
    public DateTime UpdatedAt { get; set; }
    [JsonPropertyName("last_sign_in_at")]
    public DateTime? LastSignInAt { get; set; }
}

// Authentication Response Models
public class AuthResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; } = string.Empty;
    [JsonPropertyName("token_type")]
    public string TokenType { get; set; } = "bearer";
    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }
    [JsonPropertyName("refresh_token")]
    public string RefreshToken { get; set; } = string.Empty;
    [JsonPropertyName("user")]
    public SupabaseUser? User { get; set; }
}

public class SignupResponse
{
    public SupabaseUser? User { get; set; }
    public AuthResponse? Session { get; set; }
}

// OAuth Models
public class OAuthRequest
{
    public string Provider { get; set; } = string.Empty;
    [JsonPropertyName("redirect_to")]
    public string? RedirectTo { get; set; }
    public Dictionary<string, string>? Options { get; set; }
}

public class OAuthResponse
{
    public string Url { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
}

// Error Response Model
public class AuthErrorResponse
{
    [JsonPropertyName("error")]
    public string Error { get; set; } = string.Empty;
    [JsonPropertyName("error_description")]
    public string ErrorDescription { get; set; } = string.Empty;
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    // Additional fields for Supabase API errors
    [JsonPropertyName("code")]
    public int Code { get; set; }
    [JsonPropertyName("error_code")]
    public string ErrorCode { get; set; } = string.Empty;
    [JsonPropertyName("msg")]
    public string Msg { get; set; } = string.Empty;
    [JsonPropertyName("error_id")]
    public string ErrorId { get; set; } = string.Empty;
}

// Settings Response Model
public class AuthSettingsResponse
{
    [JsonPropertyName("disable_signup")]
    public bool DisableSignup { get; set; }
    [JsonPropertyName("mailer_autoconfirm")]
    public bool MailerAutoconfirm { get; set; }
    [JsonPropertyName("phone_autoconfirm")]
    public bool PhoneAutoconfirm { get; set; }
    [JsonPropertyName("sms_provider")]
    public string? SmsProvider { get; set; }
    public AuthExternalProviders? External { get; set; }
}

public class AuthExternalProviders
{
    public bool Apple { get; set; }
    public bool Azure { get; set; }
    public bool Bitbucket { get; set; }
    public bool Discord { get; set; }
    public bool Email { get; set; }
    public bool Facebook { get; set; }
    public bool Github { get; set; }
    public bool Gitlab { get; set; }
    public bool Google { get; set; }
    public bool Keycloak { get; set; }
    public bool Linkedin { get; set; }
    public bool Notion { get; set; }
    public bool Phone { get; set; }
    public bool Saml { get; set; }
    public bool Slack { get; set; }
    public bool Spotify { get; set; }
    public bool Twitch { get; set; }
    public bool Twitter { get; set; }
    public bool Twilio { get; set; }
    public bool Workos { get; set; }
    public bool Zoom { get; set; }
}

// Request Models
public class SignupRequest
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public Dictionary<string, object>? Data { get; set; }
}

public class LoginRequest
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string? Phone { get; set; }
}

public class TokenRefreshRequest
{
    [JsonPropertyName("refresh_token")]
    public string RefreshToken { get; set; } = string.Empty;
}

public class PasswordRecoveryRequest
{
    public string Email { get; set; } = string.Empty;
    [JsonPropertyName("redirect_to")]
    public string? RedirectTo { get; set; }
}

public class OtpRequest
{
    public string Email { get; set; } = string.Empty;
    public string? Phone { get; set; }
    [JsonPropertyName("create_user")]
    public bool CreateUser { get; set; } = false;
    public Dictionary<string, object>? Data { get; set; }
}

public class VerifyRequest
{
    public string Email { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public string Token { get; set; } = string.Empty;
    public string Type { get; set; } = "signup"; // signup, recovery, email_change, phone_change
    [JsonPropertyName("redirect_to")]
    public string? RedirectTo { get; set; }
}

public class UpdateUserRequest
{
    public string? Email { get; set; }
    public string? Password { get; set; }
    public string? Phone { get; set; }
    public Dictionary<string, object>? Data { get; set; }
    [JsonPropertyName("app_metadata")]
    public Dictionary<string, object>? AppMetadata { get; set; }
    public string? Nonce { get; set; }
}

public class LinkBrokerRequest
{
    [JsonPropertyName("broker_id")]
    public string BrokerId { get; set; } = string.Empty;
    [JsonPropertyName("broker_name")]
    public string BrokerName { get; set; } = string.Empty;
    [JsonPropertyName("account_id")]
    public string? AccountId { get; set; }
}

// Generic Success Response
public class SuccessResponse
{
    public string Message { get; set; } = "Success";
}

// Google Sign-In Request Models
public class GoogleSignInRequest
{
    [JsonPropertyName("redirect_to")]
    public string? RedirectTo { get; set; }
}

public class GoogleCallbackRequest
{
    public string Code { get; set; } = string.Empty;
}

// Phone Sign-In Request Models
public class PhoneOtpRequest
{
    [JsonPropertyName("phone_number")]
    public string PhoneNumber { get; set; } = string.Empty;

    [JsonPropertyName("create_user")]
    public bool CreateUser { get; set; } = true;
}

public class PhoneSignInRequest
{
    [JsonPropertyName("phone_number")]
    public string PhoneNumber { get; set; } = string.Empty;

    [JsonPropertyName("otp_code")]
    public string OtpCode { get; set; } = string.Empty;
}
