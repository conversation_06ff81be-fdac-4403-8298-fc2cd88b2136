using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace auth_service.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class UserController : ControllerBase
{
    /// <summary>
    /// Get current authenticated user information from JWT claims
    /// </summary>
    [HttpGet("me")]
    public IActionResult Me()
    {
        try
        {
            // Extract user information from JWT claims
            var userId = User.FindFirst("sub")?.Value;
            var email = User.FindFirst("email")?.Value;
            var role = User.FindFirst("role")?.Value;
            var brokerId = User.FindFirst("broker_id")?.Value;
            var phone = User.FindFirst("phone")?.Value;
            
            // Get additional claims that might be in user_metadata or app_metadata
            var userMetadataClaim = User.FindFirst("user_metadata")?.Value;
            var appMetadataClaim = User.FindFirst("app_metadata")?.Value;
            
            // Parse metadata if available
            object? userMetadata = null;
            object? appMetadata = null;
            
            if (!string.IsNullOrEmpty(userMetadataClaim))
            {
                try
                {
                    userMetadata = System.Text.Json.JsonSerializer.Deserialize<object>(userMetadataClaim);
                }
                catch
                {
                    // Ignore parsing errors
                }
            }
            
            if (!string.IsNullOrEmpty(appMetadataClaim))
            {
                try
                {
                    appMetadata = System.Text.Json.JsonSerializer.Deserialize<object>(appMetadataClaim);
                }
                catch
                {
                    // Ignore parsing errors
                }
            }
            
            var userInfo = new
            {
                userId,
                email,
                role,
                brokerId,
                phone,
                userMetadata,
                appMetadata,
                claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList()
            };
            
            return Ok(userInfo);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to retrieve user information", details = ex.Message });
        }
    }
    
    /// <summary>
    /// Get user profile with broker information (existing functionality from AuthController)
    /// </summary>
    [HttpGet("profile")]
    public IActionResult GetProfile()
    {
        try
        {
            var userId = User.FindFirst("sub")?.Value;
            if (string.IsNullOrWhiteSpace(userId))
            {
                return Unauthorized(new { error = "User ID not found in token" });
            }
            
            // This would typically fetch from your database
            // For now, return the basic profile information from JWT
            var profile = new
            {
                id = userId,
                email = User.FindFirst("email")?.Value,
                role = User.FindFirst("role")?.Value,
                brokerId = User.FindFirst("broker_id")?.Value,
                phone = User.FindFirst("phone")?.Value,
                createdAt = User.FindFirst("iat")?.Value, // issued at timestamp
                lastSignIn = User.FindFirst("auth_time")?.Value
            };
            
            return Ok(profile);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to retrieve user profile", details = ex.Message });
        }
    }
    
    /// <summary>
    /// Check if user has specific role (example of role-based endpoint)
    /// </summary>
    [HttpGet("role/{roleName}")]
    public IActionResult CheckRole(string roleName)
    {
        try
        {
            var userRole = User.FindFirst("role")?.Value;
            var hasRole = !string.IsNullOrEmpty(userRole) && 
                         userRole.Equals(roleName, StringComparison.OrdinalIgnoreCase);
            
            return Ok(new { 
                role = roleName, 
                hasRole,
                currentRole = userRole 
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to check user role", details = ex.Message });
        }
    }
    
    /// <summary>
    /// Endpoint that requires trader role (example of policy-based authorization)
    /// </summary>
    [HttpGet("trader-only")]
    [Authorize(Policy = "IsTrader")]
    public IActionResult TraderOnly()
    {
        return Ok(new { 
            message = "This endpoint is only accessible to traders",
            userId = User.FindFirst("sub")?.Value,
            role = User.FindFirst("role")?.Value
        });
    }
    
    /// <summary>
    /// Endpoint that requires admin role
    /// </summary>
    [HttpGet("admin-only")]
    [Authorize(Policy = "IsAdmin")]
    public IActionResult AdminOnly()
    {
        return Ok(new { 
            message = "This endpoint is only accessible to admins",
            userId = User.FindFirst("sub")?.Value,
            role = User.FindFirst("role")?.Value
        });
    }
    
    /// <summary>
    /// Endpoint that requires user to have a linked broker
    /// </summary>
    [HttpGet("broker-required")]
    [Authorize(Policy = "HasBroker")]
    public IActionResult BrokerRequired()
    {
        return Ok(new { 
            message = "This endpoint requires a linked broker account",
            userId = User.FindFirst("sub")?.Value,
            brokerId = User.FindFirst("broker_id")?.Value
        });
    }

    /// <summary>
    /// Generate a test JWT token for debugging (Development only)
    /// </summary>
    [HttpPost("generate-test-token")]
    [AllowAnonymous]
    public IActionResult GenerateTestToken()
    {
        var env = HttpContext.RequestServices.GetRequiredService<IWebHostEnvironment>();
        if (!env.IsDevelopment())
        {
            return NotFound();
        }

        try
        {
            var configuration = HttpContext.RequestServices.GetRequiredService<IConfiguration>();
            var supabaseJwtSecret = configuration["Supabase:JwtSecret"] ?? Environment.GetEnvironmentVariable("SUPABASE_JWT_SECRET");
            var supabaseUrl = configuration["Supabase:Url"] ?? Environment.GetEnvironmentVariable("SUPABASE_URL");

            if (string.IsNullOrEmpty(supabaseJwtSecret) || string.IsNullOrEmpty(supabaseUrl))
            {
                return BadRequest("Supabase configuration not found");
            }

            var key = Encoding.UTF8.GetBytes(supabaseJwtSecret);
            var tokenHandler = new JwtSecurityTokenHandler();

            var claims = new[]
            {
                new Claim("sub", "cdbb0857-ca19-4704-b0c2-130043e684b0"),
                new Claim("email", "<EMAIL>"),
                new Claim("aud", "authenticated"),
                new Claim("role", "authenticated"),
                new Claim("iss", $"{supabaseUrl}/auth/v1"),
                new Claim("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
                new Claim("exp", DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(1),
                Issuer = $"{supabaseUrl}/auth/v1",
                Audience = "authenticated",
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            // Don't set kid header - let it be generated naturally

            var token = tokenHandler.CreateToken(tokenDescriptor);
            var tokenString = tokenHandler.WriteToken(token);

            return Ok(new { access_token = tokenString });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to generate test token", details = ex.Message });
        }
    }
}
