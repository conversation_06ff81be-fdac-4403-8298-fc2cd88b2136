#!/bin/bash

# Quick fix script for SSL services with missing environment variables
# This script fixes the MarketData and Auth services that are failing due to missing Supabase environment variables

set -e

echo "🔧 Fixing SSL services with missing environment variables..."

# Check if we're in the right directory
if [ ! -f "docker-compose.ssl.yml" ]; then
    echo "❌ docker-compose.ssl.yml not found. Please run this script from the project root directory."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please ensure environment variables are configured."
    exit 1
fi

# Load environment variables
echo "📋 Loading environment variables..."
set -a
source .env
set +a

# Verify critical environment variables
echo "🔍 Checking critical environment variables..."
if [ -z "$SUPABASE_URL" ]; then
    echo "❌ SUPABASE_URL is not set in .env file"
    exit 1
fi

if [ -z "$SUPABASE_JWT_SECRET" ]; then
    echo "❌ SUPABASE_JWT_SECRET is not set in .env file"
    exit 1
fi

echo "✅ Environment variables verified"

# Stop failing services
echo "🛑 Stopping failing services..."
docker compose -f docker-compose.ssl.yml stop marketdata-service auth-service || true

# Remove containers to ensure clean restart
echo "🗑️ Removing old containers..."
docker compose -f docker-compose.ssl.yml rm -f marketdata-service auth-service || true

# Clean up any orphaned containers
docker container prune -f || true

# Rebuild services with updated configuration
echo "🔨 Rebuilding services..."
docker compose -f docker-compose.ssl.yml build --no-cache marketdata-service auth-service

# Start services in order
echo "🚀 Starting services..."

# Start Redis first (dependency)
docker compose -f docker-compose.ssl.yml up -d redis
echo "⏳ Waiting for Redis to be ready..."
sleep 10

# Start Auth service
docker compose -f docker-compose.ssl.yml up -d auth-service
echo "⏳ Waiting for Auth service to start..."
sleep 30

# Start MarketData service
docker compose -f docker-compose.ssl.yml up -d marketdata-service
echo "⏳ Waiting for MarketData service to start..."
sleep 30

# Start Thread service
docker compose -f docker-compose.ssl.yml up -d thread-service
echo "⏳ Waiting for Thread service to start..."
sleep 30

# Check service status
echo "📊 Checking service status..."
docker compose -f docker-compose.ssl.yml ps

# Test services
echo "🧪 Testing services..."

# Test MarketData service
if curl -f http://localhost:5001/health --connect-timeout 10 >/dev/null 2>&1; then
    echo "✅ MarketData service is healthy"
else
    echo "⚠️ MarketData service health check failed"
    echo "📋 MarketData service logs:"
    docker compose -f docker-compose.ssl.yml logs --tail=20 marketdata-service
fi

# Test Auth service
if curl -f http://localhost:5002/health --connect-timeout 10 >/dev/null 2>&1; then
    echo "✅ Auth service is healthy"
else
    echo "⚠️ Auth service health check failed"
    echo "📋 Auth service logs:"
    docker compose -f docker-compose.ssl.yml logs --tail=20 auth-service
fi

# Test Thread service
if curl -f http://localhost:5004/health --connect-timeout 10 >/dev/null 2>&1; then
    echo "✅ Thread service is healthy"
else
    echo "⚠️ Thread service health check failed"
    echo "📋 Thread service logs:"
    docker compose -f docker-compose.ssl.yml logs --tail=20 thread-service
fi

# Final status
echo ""
echo "🎉 Service fix completed!"
echo "📊 Final service status:"
docker compose -f docker-compose.ssl.yml ps

# Check for any remaining issues
RESTARTING_COUNT=$(docker compose -f docker-compose.ssl.yml ps | grep -c "Restarting" || echo "0")
if [ "$RESTARTING_COUNT" -gt 0 ]; then
    echo "⚠️ Warning: $RESTARTING_COUNT services are still restarting"
    echo "📋 Check logs for details:"
    echo "  docker compose -f docker-compose.ssl.yml logs --tail=50 <service-name>"
else
    echo "✅ All services are stable!"
fi

echo ""
echo "🌐 Your API should now be accessible at:"
echo "  - HTTP:  http://abraapp.undeclab.com"
echo "  - HTTPS: https://abraapp.undeclab.com (if SSL is configured)"
