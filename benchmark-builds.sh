#!/bin/bash

# Docker Build Performance Benchmark Script
# Compares build times with and without optimizations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to format time
format_time() {
    local seconds=$1
    printf "%02d:%02d" $((seconds/60)) $((seconds%60))
}

# Function to benchmark build
benchmark_build() {
    local method=$1
    local description=$2
    
    print_status "Benchmarking: $description"
    
    # Clean up before build
    docker system prune -f > /dev/null 2>&1
    
    local start_time=$(date +%s)
    
    case $method in
        "standard")
            docker-compose build --no-cache > /dev/null 2>&1
            ;;
        "optimized")
            export DOCKER_BUILDKIT=1
            export COMPOSE_DOCKER_CLI_BUILD=1
            docker-compose build --parallel --compress > /dev/null 2>&1
            ;;
        "cached")
            export DOCKER_BUILDKIT=1
            export COMPOSE_DOCKER_CLI_BUILD=1
            docker-compose build --parallel > /dev/null 2>&1
            ;;
    esac
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "$duration"
}

# Function to get image sizes
get_image_sizes() {
    local total_size=0
    local services=("abraapi-assistant-service" "abraapi-auth-service" "abraapi-marketdata-service" "abraapi-thread-service")
    
    for service in "${services[@]}"; do
        if docker images --format "table {{.Size}}" "$service:latest" 2>/dev/null | tail -n +2 | head -1 > /dev/null; then
            local size=$(docker images --format "{{.Size}}" "$service:latest" 2>/dev/null | head -1)
            echo "  $service: $size"
        fi
    done
}

# Function to run comprehensive benchmark
run_benchmark() {
    print_status "🚀 Starting Docker Build Performance Benchmark"
    echo ""
    
    # Store results
    declare -A results
    
    # Test 1: Standard build (no optimizations)
    print_status "Test 1: Standard build (no optimizations)"
    export DOCKER_BUILDKIT=0
    unset COMPOSE_DOCKER_CLI_BUILD
    results["standard"]=$(benchmark_build "standard" "Standard Docker build")
    
    # Test 2: Optimized build (first time)
    print_status "Test 2: Optimized build with BuildKit (first time)"
    results["optimized"]=$(benchmark_build "optimized" "Optimized build with BuildKit")
    
    # Test 3: Cached build
    print_status "Test 3: Cached build (with existing layers)"
    results["cached"]=$(benchmark_build "cached" "Cached build with existing layers")
    
    # Display results
    echo ""
    print_success "📊 Benchmark Results:"
    echo "┌─────────────────────────────┬─────────────┬─────────────────┐"
    echo "│ Build Method                │ Time        │ Improvement     │"
    echo "├─────────────────────────────┼─────────────┼─────────────────┤"
    
    local standard_time=${results["standard"]}
    local optimized_time=${results["optimized"]}
    local cached_time=${results["cached"]}
    
    printf "│ %-27s │ %11s │ %15s │\n" "Standard Build" "$(format_time $standard_time)" "Baseline"
    
    local optimized_improvement=$(( (standard_time - optimized_time) * 100 / standard_time ))
    printf "│ %-27s │ %11s │ %14s%% │\n" "Optimized Build" "$(format_time $optimized_time)" "$optimized_improvement"
    
    local cached_improvement=$(( (standard_time - cached_time) * 100 / standard_time ))
    printf "│ %-27s │ %11s │ %14s%% │\n" "Cached Build" "$(format_time $cached_time)" "$cached_improvement"
    
    echo "└─────────────────────────────┴─────────────┴─────────────────┘"
    
    echo ""
    print_success "📦 Final Image Sizes:"
    get_image_sizes
    
    echo ""
    print_success "🎯 Optimization Summary:"
    echo "  • BuildKit enabled: $([ "$DOCKER_BUILDKIT" = "1" ] && echo "✅ Yes" || echo "❌ No")"
    echo "  • Parallel builds: ✅ Yes"
    echo "  • Layer caching: ✅ Yes"
    echo "  • Multi-stage builds: ✅ Yes"
    echo "  • Alpine runtime: ✅ Yes"
    echo "  • Non-root user: ✅ Yes"
    echo "  • .dockerignore: ✅ Yes"
    
    echo ""
    if [ $optimized_improvement -gt 30 ]; then
        print_success "🚀 Excellent optimization! Build time improved by ${optimized_improvement}%"
    elif [ $optimized_improvement -gt 15 ]; then
        print_success "✅ Good optimization! Build time improved by ${optimized_improvement}%"
    else
        print_warning "⚠️  Moderate optimization. Build time improved by ${optimized_improvement}%"
    fi
}

# Function to show help
show_help() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Benchmark Docker build performance with different optimization strategies"
    echo ""
    echo "Options:"
    echo "  -h, --help    Show this help message"
    echo "  --quick       Run quick benchmark (skip standard build)"
    echo "  --clean       Clean all Docker resources before benchmark"
    echo ""
    echo "This script will:"
    echo "  1. Test standard Docker build (no optimizations)"
    echo "  2. Test optimized build with BuildKit and parallel builds"
    echo "  3. Test cached build performance"
    echo "  4. Compare results and show improvements"
}

# Function to clean Docker resources
clean_docker() {
    print_status "🧹 Cleaning Docker resources..."
    docker system prune -a -f
    docker builder prune -a -f
    print_success "Docker resources cleaned"
}

# Parse command line arguments
QUICK_MODE=false
CLEAN_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --quick)
            QUICK_MODE=true
            shift
            ;;
        --clean)
            CLEAN_MODE=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    if [ "$CLEAN_MODE" = true ]; then
        clean_docker
        echo ""
    fi
    
    if [ "$QUICK_MODE" = true ]; then
        print_warning "Quick mode: Skipping standard build test"
        # Run only optimized tests
        export DOCKER_BUILDKIT=1
        export COMPOSE_DOCKER_CLI_BUILD=1
        
        print_status "Running optimized build test..."
        local optimized_time=$(benchmark_build "optimized" "Optimized build")
        
        print_status "Running cached build test..."
        local cached_time=$(benchmark_build "cached" "Cached build")
        
        echo ""
        print_success "📊 Quick Benchmark Results:"
        echo "  Optimized build: $(format_time $optimized_time)"
        echo "  Cached build: $(format_time $cached_time)"
        
        local cache_improvement=$(( (optimized_time - cached_time) * 100 / optimized_time ))
        echo "  Cache improvement: ${cache_improvement}%"
    else
        run_benchmark
    fi
    
    echo ""
    print_success "🎉 Benchmark completed!"
    print_status "Use './build-optimized.sh' for optimized builds in the future"
}

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is required but not installed"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    print_error "Docker is not running"
    exit 1
fi

# Run main function
main