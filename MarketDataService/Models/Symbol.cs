namespace MarketDataService.Models;

/// <summary>
/// Represents a trading symbol from a broker
/// </summary>
public class Symbol
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string MarketType { get; set; } = string.Empty; // stocks, forex, crypto
    public string Exchange { get; set; } = string.Empty;
    public string Currency { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string BrokerId { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime? LastUpdated { get; set; }
}

/// <summary>
/// Represents a symbol with activity metrics
/// </summary>
public class ActiveSymbol : Symbol
{
    public decimal? Volume { get; set; }
    public decimal? VolumeChange { get; set; }
    public decimal? Price { get; set; }
    public decimal? PriceChange { get; set; }
    public decimal? PriceChangePercent { get; set; }
    public int Rank { get; set; }
}

/// <summary>
/// Represents a symbol search result with relevance scoring
/// </summary>
public class SymbolSearchResult : Symbol
{
    public double RelevanceScore { get; set; }
    public string MatchType { get; set; } = string.Empty; // symbol, name, description
    public string? Description { get; set; }
}

/// <summary>
/// Request model for symbol search
/// </summary>
public class SymbolSearchRequest
{
    public string Query { get; set; } = string.Empty;
    public string? MarketType { get; set; }
    public string? BrokerId { get; set; }
    public int Limit { get; set; } = 20;
}

/// <summary>
/// Request model for getting most active symbols
/// </summary>
public class MostActiveSymbolsRequest
{
    public string? MarketType { get; set; }
    public string? BrokerId { get; set; }
    public int Limit { get; set; } = 50;
}

/// <summary>
/// Response model for symbol operations
/// </summary>
public class SymbolResponse<T>
{
    public List<T> Symbols { get; set; } = new();
    public int TotalCount { get; set; }
    public string BrokerId { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public TimeSpan ProcessingTime { get; set; }
}
