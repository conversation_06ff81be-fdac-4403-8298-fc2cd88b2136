namespace MarketDataService.Models;

public class WatchlistDto
{
    public long Id { get; set; }
    public string Name { get; set; } = default!;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public bool? IsGlobal { get; set; }
    public int ItemCount { get; set; }
    public List<WatchlistItemWithPriceDto> Items { get; set; } = new();
}

public class WatchlistItemDto
{
    public long Id { get; set; }
    public string Symbol { get; set; } = default!;
    public string? Broker { get; set; }
    public DateTime? AddedAt { get; set; }
    public int SortOrder { get; set; } = 0; // Default for display purposes
}

public class WatchlistItemWithPriceDto : WatchlistItemDto
{
    public decimal? CurrentPrice { get; set; }
    public decimal? PreviousPrice { get; set; }
    public decimal? PriceChange { get; set; }
    public decimal? PriceChangePercent { get; set; }
    public DateTime? LastUpdated { get; set; }
    public string? Status { get; set; } // "success", "error", "loading"
}

public class CreateWatchlistRequest
{
    public string Name { get; set; } = default!;
    public bool? IsGlobal { get; set; } = false;
    public List<string> Symbols { get; set; } = new();
}

public class UpdateWatchlistRequest
{
    public string? Name { get; set; }
    public bool? IsGlobal { get; set; }
}

public class AddSymbolsRequest
{
    public List<string> Symbols { get; set; } = default!;
}

public class ReorderItemsRequest
{
    public List<ReorderItem> Items { get; set; } = default!;
}

public class ReorderItem
{
    public int Id { get; set; }
    public int SortOrder { get; set; }
}

public class BulkPriceRequest
{
    public List<string> Symbols { get; set; } = default!;
    public bool BypassCache { get; set; } = false;
}

public class BulkPriceResponse
{
    public List<SymbolPriceDto> Prices { get; set; } = new();
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<string> FailedSymbols { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string? CacheStatus { get; set; }
    public TimeSpan ProcessingTime { get; set; }
}

public class SymbolPriceDto
{
    public string Symbol { get; set; } = default!;
    public decimal? CurrentPrice { get; set; }
    public decimal? PreviousPrice { get; set; }
    public decimal? PriceChange { get; set; }
    public decimal? PriceChangePercent { get; set; }
    public decimal? Volume { get; set; }
    public DateTime? LastUpdated { get; set; }
    public string? Status { get; set; }
    public string? BrokerId { get; set; }
}

public class WatchlistWithPricesDto
{
    public long Id { get; set; }
    public string Name { get; set; } = default!;
    public Guid? UserId { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public bool? IsGlobal { get; set; }
    public List<WatchlistItemWithPriceDto> Items { get; set; } = new();
    public int TotalItems { get; set; }
    public DateTime PricesLastUpdated { get; set; }
}

public class PriceAnalyticsDto
{
    public string Symbol { get; set; } = default!;
    public decimal? CurrentPrice { get; set; }
    public decimal? DayChange { get; set; }
    public decimal? DayChangePercent { get; set; }
    public decimal? WeekChange { get; set; }
    public decimal? WeekChangePercent { get; set; }
    public decimal? MonthChange { get; set; }
    public decimal? MonthChangePercent { get; set; }
    public decimal? Volume { get; set; }
    public decimal? AverageVolume { get; set; }
    public decimal? High52Week { get; set; }
    public decimal? Low52Week { get; set; }
    public DateTime? LastUpdated { get; set; }
}

public class BulkSymbolRequest
{
    public List<string> Symbols { get; set; } = new();
    public string? BrokerId { get; set; }
}
