using MarketDataService.Models;

namespace MarketDataService.Interfaces;

public interface ISymbolProvider
{
    /// <summary>
    /// The unique identifier for this broker
    /// </summary>
    string BrokerId { get; }

    /// <summary>
    /// Gets all available symbols from the broker
    /// </summary>
    Task<List<Symbol>> GetAllSymbolsAsync();

    /// <summary>
    /// Gets symbols filtered by market type (stocks, forex, crypto)
    /// </summary>
    Task<List<Symbol>> GetSymbolsByMarketTypeAsync(string marketType);

    /// <summary>
    /// Gets the most traded/active symbols
    /// </summary>
    Task<List<ActiveSymbol>> GetMostActiveSymbolsAsync(string? marketType = null, int limit = 50);

    /// <summary>
    /// Searches for symbols based on query string
    /// </summary>
    Task<List<SymbolSearchResult>> SearchSymbolsAsync(string query, string? marketType = null, int limit = 20);
}
