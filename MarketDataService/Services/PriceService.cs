using MarketDataService.Configuration;
using MarketDataService.Data;
using MarketDataService.Interfaces;
using MarketDataService.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace MarketDataService.Services;

public class PriceService : IPriceService
{
    private readonly MarketDataContext _db;
    private readonly IBrokerRegistry _registry;
    private readonly IRedisCacheService _cache;
    private readonly BrokerSettings _settings;

    public PriceService(
        MarketDataContext db,
        IBrokerRegistry registry,
        IRedisCacheService cache,
        IOptions<BrokerSettings> settings)
    {
        _db = db;
        _registry = registry;
        _cache = cache;
        _settings = settings.Value;
    }

    public async Task<MarketData?> GetLatestPriceAsync(string symbol, string? brokerId = null, bool bypassCache = false)
    {
        var cacheKey = $"price:{brokerId}:{symbol.ToUpper()}";

        if (!bypassCache)
        {
            var cached = await _cache.GetAsync<MarketData>(cacheKey);
            if (cached != null) return cached;
        }

        var tried = new HashSet<string>();
        var fallbackOrder = new[] { brokerId, "finnhub", "polygon" };

        foreach (var broker in fallbackOrder.Distinct())
        {
            if (broker != null) tried.Add(broker);
            var provider = broker != null ? _registry.GetProvider(broker) : null;
            if (provider == null) continue;

            var price = await provider.FetchPriceAsync(symbol);
            if (price != null)
            {
                var entry = new MarketData
                {
                    Symbol = symbol.ToUpper(),
                    Price = price.Value,
                    Timestamp = DateTime.UtcNow
                };

                _db.MarketDataEntries.Add(entry);
                await _db.SaveChangesAsync();
                await _cache.SetAsync(cacheKey, entry, TimeSpan.FromMinutes(_settings.CacheMinutes));
                return entry;
            }
        }

        return null;
    }
}