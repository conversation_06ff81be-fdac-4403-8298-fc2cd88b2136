using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Services;

public class MockHistoricalPriceRegistry : IHistoricalPriceRegistry
{
    private readonly ILogger<MockHistoricalPriceRegistry> _logger;

    public MockHistoricalPriceRegistry(ILogger<MockHistoricalPriceRegistry> logger)
    {
        _logger = logger;
    }

    public IHistoricalPriceProvider? GetProvider(string brokerId)
    {
        _logger.LogInformation("Mock: Getting historical price provider for broker {BrokerId}", brokerId);
        return new MockHistoricalPriceProvider(_logger);
    }
}

public class MockHistoricalPriceProvider : IHistoricalPriceProvider
{
    private readonly ILogger _logger;

    public string BrokerId { get; set; } = "mock";

    public MockHistoricalPriceProvider(ILogger logger)
    {
        _logger = logger;
    }

    public async Task<List<HistoricalPrice>?> GetHistoricalPricesAsync(string symbol, DateTime from, DateTime to, string interval = "1d")
    {
        _logger.LogInformation("Mock: Getting historical prices for {Symbol} from {From} to {To}", symbol, from, to);
        
        await Task.Delay(100); // Simulate API call
        
        var prices = new List<HistoricalPrice>();
        var current = from;
        var random = new Random();
        var basePrice = 150.00m;
        
        while (current <= to && prices.Count < 100) // Limit to 100 data points
        {
            prices.Add(new HistoricalPrice
            {
                Date = current,
                Open = basePrice + (decimal)(random.NextDouble() * 10 - 5),
                High = basePrice + (decimal)(random.NextDouble() * 15),
                Low = basePrice - (decimal)(random.NextDouble() * 10),
                Close = basePrice + (decimal)(random.NextDouble() * 10 - 5),
                Volume = random.Next(500000, 2000000)
            });
            
            current = interval switch
            {
                "1h" => current.AddHours(1),
                "1d" => current.AddDays(1),
                "1w" => current.AddDays(7),
                "1m" => current.AddMonths(1),
                _ => current.AddDays(1)
            };
        }
        
        return prices;
    }
}
