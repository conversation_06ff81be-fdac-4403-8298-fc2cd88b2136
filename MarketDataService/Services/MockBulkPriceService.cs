using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Services;

public class MockBulkPriceService : IBulkPriceService
{
    private readonly ILogger<MockBulkPriceService> _logger;

    public MockBulkPriceService(ILogger<MockBulkPriceService> logger)
    {
        _logger = logger;
    }

    public async Task<BulkPriceResponse> GetBulkPricesAsync(List<string> symbols, string? brokerId = null, bool bypassCache = false)
    {
        _logger.LogInformation("Mock: Getting bulk prices for {Count} symbols with broker {BrokerId}", symbols.Count, brokerId);
        
        await Task.Delay(100); // Simulate API call
        
        var prices = new List<SymbolPriceDto>();
        var random = new Random();
        
        foreach (var symbol in symbols)
        {
            var basePrice = 100m + (decimal)(random.NextDouble() * 200); // Random price between 100-300
            
            prices.Add(new SymbolPriceDto
            {
                Symbol = symbol.ToUpper(),
                CurrentPrice = basePrice,
                PreviousPrice = basePrice - (decimal)(random.NextDouble() * 10 - 5), // Random change
                PriceChange = (decimal)(random.NextDouble() * 10 - 5),
                PriceChangePercent = (decimal)(random.NextDouble() * 10 - 5),
                Volume = random.Next(100000, 5000000),
                LastUpdated = DateTime.UtcNow,
                Status = "success",
                BrokerId = brokerId ?? "mock"
            });
        }
        
        return new BulkPriceResponse
        {
            Prices = prices,
            SuccessCount = prices.Count,
            FailureCount = 0,
            FailedSymbols = new List<string>(),
            Timestamp = DateTime.UtcNow,
            CacheStatus = "mock",
            ProcessingTime = TimeSpan.FromMilliseconds(100)
        };
    }

    public async Task<List<SymbolPriceDto>> GetSymbolPricesWithHistoryAsync(List<string> symbols, string? brokerId = null)
    {
        _logger.LogInformation("Mock: Getting symbol prices with history for {Count} symbols", symbols.Count);

        var bulkResponse = await GetBulkPricesAsync(symbols, brokerId);
        return bulkResponse.Prices;
    }

    public async Task<WatchlistWithPricesDto> GetWatchlistWithPricesAsync(long watchlistId, Guid userId)
    {
        _logger.LogInformation("Mock: Getting watchlist {WatchlistId} with prices for user {UserId}", watchlistId, userId);

        await Task.Delay(50);

        // Return mock watchlist with prices
        return new WatchlistWithPricesDto
        {
            Id = watchlistId,
            Name = "Mock Watchlist",
            UserId = userId,
            CreatedAt = DateTime.UtcNow.AddDays(-7),
            UpdatedAt = DateTime.UtcNow,
            IsGlobal = true,
            TotalItems = 2,
            PricesLastUpdated = DateTime.UtcNow,
            Items = new List<WatchlistItemWithPriceDto>
            {
                new WatchlistItemWithPriceDto
                {
                    Id = 1,
                    Symbol = "AAPL",
                    AddedAt = DateTime.UtcNow.AddDays(-5),
                    SortOrder = 1,
                    CurrentPrice = 150.25m,
                    PreviousPrice = 148.50m,
                    PriceChange = 1.75m,
                    PriceChangePercent = 1.18m,
                    LastUpdated = DateTime.UtcNow,
                    Status = "success"
                },
                new WatchlistItemWithPriceDto
                {
                    Id = 2,
                    Symbol = "GOOGL",
                    AddedAt = DateTime.UtcNow.AddDays(-3),
                    SortOrder = 2,
                    CurrentPrice = 2750.80m,
                    PreviousPrice = 2745.20m,
                    PriceChange = 5.60m,
                    PriceChangePercent = 0.20m,
                    LastUpdated = DateTime.UtcNow,
                    Status = "success"
                }
            }
        };
    }

    public async Task<List<PriceAnalyticsDto>> GetPriceAnalyticsAsync(List<string> symbols, string? brokerId = null)
    {
        _logger.LogInformation("Mock: Getting price analytics for {Count} symbols", symbols.Count);

        await Task.Delay(50);

        var analytics = new List<PriceAnalyticsDto>();
        var random = new Random();

        foreach (var symbol in symbols)
        {
            var currentPrice = 100m + (decimal)(random.NextDouble() * 200);

            analytics.Add(new PriceAnalyticsDto
            {
                Symbol = symbol.ToUpper(),
                CurrentPrice = currentPrice,
                DayChange = (decimal)(random.NextDouble() * 10 - 5),
                DayChangePercent = (decimal)(random.NextDouble() * 5 - 2.5),
                WeekChange = (decimal)(random.NextDouble() * 20 - 10),
                WeekChangePercent = (decimal)(random.NextDouble() * 10 - 5),
                MonthChange = (decimal)(random.NextDouble() * 40 - 20),
                MonthChangePercent = (decimal)(random.NextDouble() * 15 - 7.5),
                Volume = random.Next(100000, 10000000),
                AverageVolume = random.Next(500000, 5000000),
                High52Week = currentPrice + (decimal)(random.NextDouble() * 50),
                Low52Week = currentPrice - (decimal)(random.NextDouble() * 50),
                LastUpdated = DateTime.UtcNow
            });
        }

        return analytics;
    }
}
