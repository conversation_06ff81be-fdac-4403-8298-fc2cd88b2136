using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Services;

public class MockPriceService : IPriceService
{
    private readonly ILogger<MockPriceService> _logger;

    public MockPriceService(ILogger<MockPriceService> logger)
    {
        _logger = logger;
    }

    public async Task<MarketData?> GetLatestPriceAsync(string symbol, string? brokerId = null, bool bypassCache = false)
    {
        _logger.LogInformation("Mock: Getting price for {Symbol} from broker {BrokerId}", symbol, brokerId);
        
        // Return mock data for testing
        await Task.Delay(100); // Simulate API call
        
        return new MarketData
        {
            Symbol = symbol.ToUpper(),
            Price = 150.00m + (decimal)(new Random().NextDouble() * 10), // Random price between 150-160
            Timestamp = DateTime.UtcNow
        };
    }
}
