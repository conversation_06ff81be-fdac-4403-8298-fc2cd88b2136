using MarketDataService.Interfaces;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Concurrent;
using System.Text.Json;

namespace MarketDataService.Services;

public class EnhancedCacheService : IRedisCacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IRedisCacheService _distributedCache;
    private readonly ILogger<EnhancedCacheService> _logger;
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _locks = new();
    private readonly ConcurrentDictionary<string, CacheStatistics> _stats = new();

    public EnhancedCacheService(
        IMemoryCache memoryCache,
        IRedisCacheService distributedCache,
        ILogger<EnhancedCacheService> logger)
    {
        _memoryCache = memoryCache;
        _distributedCache = distributedCache;
        _logger = logger;
    }

    public async Task<T?> GetAsync<T>(string key) where T : class
    {
        var stats = GetOrCreateStats(key);
        stats.RequestCount++;

        // Try L1 cache (memory) first
        if (_memoryCache.TryGetValue(key, out var memoryValue) && memoryValue is T typedMemoryValue)
        {
            stats.L1HitCount++;
            _logger.LogDebug("L1 cache hit for key: {Key}", key);
            return typedMemoryValue;
        }

        // Try L2 cache (distributed) next
        var distributedValue = await _distributedCache.GetAsync<T>(key);
        if (distributedValue != null)
        {
            stats.L2HitCount++;
            _logger.LogDebug("L2 cache hit for key: {Key}", key);
            
            // Populate L1 cache with shorter expiry
            var l1Options = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(2),
                Priority = CacheItemPriority.High
            };
            _memoryCache.Set(key, distributedValue, l1Options);
            
            return distributedValue;
        }

        stats.MissCount++;
        _logger.LogDebug("Cache miss for key: {Key}", key);
        return null;
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
    {
        var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(5);
        
        // Set in both L1 and L2 caches
        var l1Options = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(Math.Min(effectiveExpiry.TotalMinutes, 10)),
            Priority = CacheItemPriority.High
        };
        _memoryCache.Set(key, value, l1Options);
        
        await _distributedCache.SetAsync(key, value, effectiveExpiry);
        
        _logger.LogDebug("Set cache key: {Key} with expiry: {Expiry}", key, effectiveExpiry);
    }

    public async Task RemoveAsync(string key)
    {
        _memoryCache.Remove(key);
        await _distributedCache.RemoveAsync(key);
        _logger.LogDebug("Removed cache key: {Key}", key);
    }

    public async Task<bool> ExistsAsync(string key)
    {
        if (_memoryCache.TryGetValue(key, out _))
            return true;
            
        return await _distributedCache.ExistsAsync(key);
    }

    /// <summary>
    /// Get or set with cache-aside pattern and distributed locking to prevent cache stampede
    /// </summary>
    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan? expiry = null) where T : class
    {
        var cached = await GetAsync<T>(key);
        if (cached != null)
            return cached;

        // Use distributed locking to prevent cache stampede
        var lockKey = $"lock:{key}";
        var semaphore = _locks.GetOrAdd(lockKey, _ => new SemaphoreSlim(1, 1));

        await semaphore.WaitAsync();
        try
        {
            // Double-check after acquiring lock
            cached = await GetAsync<T>(key);
            if (cached != null)
                return cached;

            // Generate the value
            var value = await factory();
            if (value != null)
            {
                await SetAsync(key, value, expiry);
            }
            
            return value;
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <summary>
    /// Warm up cache with commonly accessed data
    /// </summary>
    public async Task WarmupAsync(Dictionary<string, Func<Task<object>>> warmupTasks)
    {
        _logger.LogInformation("Starting cache warmup with {Count} tasks", warmupTasks.Count);
        
        var tasks = warmupTasks.Select(async kvp =>
        {
            try
            {
                var value = await kvp.Value();
                if (value != null)
                {
                    await SetAsync(kvp.Key, value, TimeSpan.FromMinutes(30));
                    _logger.LogDebug("Warmed up cache key: {Key}", kvp.Key);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to warm up cache key: {Key}", kvp.Key);
            }
        });

        await Task.WhenAll(tasks);
        _logger.LogInformation("Cache warmup completed");
    }

    /// <summary>
    /// Get cache statistics for monitoring
    /// </summary>
    public Dictionary<string, CacheStatistics> GetStatistics()
    {
        return new Dictionary<string, CacheStatistics>(_stats);
    }

    /// <summary>
    /// Clear cache statistics
    /// </summary>
    public void ClearStatistics()
    {
        _stats.Clear();
    }

    private CacheStatistics GetOrCreateStats(string key)
    {
        return _stats.GetOrAdd(key, _ => new CacheStatistics());
    }
}

public class CacheStatistics
{
    public long RequestCount { get; set; }
    public long L1HitCount { get; set; }
    public long L2HitCount { get; set; }
    public long MissCount { get; set; }
    
    public double L1HitRatio => RequestCount > 0 ? (double)L1HitCount / RequestCount : 0;
    public double L2HitRatio => RequestCount > 0 ? (double)L2HitCount / RequestCount : 0;
    public double OverallHitRatio => RequestCount > 0 ? (double)(L1HitCount + L2HitCount) / RequestCount : 0;
    public double MissRatio => RequestCount > 0 ? (double)MissCount / RequestCount : 0;
}