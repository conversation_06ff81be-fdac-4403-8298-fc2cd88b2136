using MarketDataService.Interfaces;

namespace MarketDataService.Services;

public class HistoricalPriceRegistry : IHistoricalPriceRegistry
{
    private readonly Dictionary<string, IHistoricalPriceProvider> _providers;

    public HistoricalPriceRegistry(IEnumerable<IHistoricalPriceProvider> providers)
    {
        _providers = providers.ToDictionary(p => p.BrokerId.ToLower());
    }

    public IHistoricalPriceProvider? GetProvider(string brokerId)
    {
        _providers.TryGetValue(brokerId.ToLower(), out var provider);
        return provider;
    }
}