using System.Diagnostics;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Services;

public class BulkPriceService : IBulkPriceService
{
    private readonly IPriceService _priceService;
    private readonly IWatchlistService _watchlistService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<BulkPriceService> _logger;
    
    private const int MaxConcurrentRequests = 10;
    private const int CacheExpirationMinutes = 2;

    public BulkPriceService(
        IPriceService priceService,
        IWatchlistService watchlistService,
        IMemoryCache cache,
        ILogger<BulkPriceService> logger)
    {
        _priceService = priceService;
        _watchlistService = watchlistService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<BulkPriceResponse> GetBulkPricesAsync(List<string> symbols, string? brokerId = null, bool bypassCache = false)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new BulkPriceResponse();
        
        try
        {
            _logger.LogInformation("Fetching bulk prices for {Count} symbols", symbols.Count);

            // Check cache first if not bypassing
            var cachedPrices = new Dictionary<string, SymbolPriceDto>();
            var symbolsToFetch = new List<string>();

            if (!bypassCache)
            {
                foreach (var symbol in symbols)
                {
                    var cacheKey = $"price_{symbol}_{brokerId ?? "default"}";
                    if (_cache.TryGetValue(cacheKey, out SymbolPriceDto? cachedPrice) && cachedPrice != null)
                    {
                        cachedPrices[symbol] = cachedPrice;
                    }
                    else
                    {
                        symbolsToFetch.Add(symbol);
                    }
                }
                
                response.CacheStatus = $"Cache hits: {cachedPrices.Count}, Cache misses: {symbolsToFetch.Count}";
            }
            else
            {
                symbolsToFetch = symbols;
                response.CacheStatus = "Cache bypassed";
            }

            // Fetch prices for symbols not in cache using parallel processing
            var fetchedPrices = new List<SymbolPriceDto>();
            if (symbolsToFetch.Any())
            {
                var semaphore = new SemaphoreSlim(MaxConcurrentRequests);
                var fetchTasks = symbolsToFetch.Select(async symbol =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        return await FetchSinglePriceAsync(symbol, brokerId);
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                var results = await Task.WhenAll(fetchTasks);
                fetchedPrices = results.Where(p => p != null).ToList()!;

                // Cache the fetched prices
                foreach (var price in fetchedPrices)
                {
                    var cacheKey = $"price_{price.Symbol}_{brokerId ?? "default"}";
                    _cache.Set(cacheKey, price, TimeSpan.FromMinutes(CacheExpirationMinutes));
                }
            }

            // Combine cached and fetched prices
            response.Prices.AddRange(cachedPrices.Values);
            response.Prices.AddRange(fetchedPrices);

            // Calculate statistics
            response.SuccessCount = response.Prices.Count;
            response.FailureCount = symbols.Count - response.SuccessCount;
            response.FailedSymbols = symbols.Except(response.Prices.Select(p => p.Symbol)).ToList();

            stopwatch.Stop();
            response.ProcessingTime = stopwatch.Elapsed;

            _logger.LogInformation("Bulk price fetch completed: {Success} successful, {Failed} failed, {Time}ms", 
                response.SuccessCount, response.FailureCount, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching bulk prices");
            stopwatch.Stop();
            response.ProcessingTime = stopwatch.Elapsed;
            response.FailedSymbols = symbols;
            response.FailureCount = symbols.Count;
            return response;
        }
    }

    public async Task<List<SymbolPriceDto>> GetSymbolPricesWithHistoryAsync(List<string> symbols, string? brokerId = null)
    {
        var bulkResponse = await GetBulkPricesAsync(symbols, brokerId);
        
        // Add historical price calculations
        foreach (var price in bulkResponse.Prices)
        {
            await EnrichWithHistoricalDataAsync(price, brokerId);
        }

        return bulkResponse.Prices;
    }

    public async Task<WatchlistWithPricesDto> GetWatchlistWithPricesAsync(long watchlistId, Guid userId)
    {
        try
        {
            // Get watchlist with items
            var watchlist = await _watchlistService.GetWatchlistAsync(userId, watchlistId);
            if (watchlist == null)
            {
                throw new ArgumentException($"Watchlist {watchlistId} not found for user {userId}");
            }

            // Extract symbols from watchlist items
            var symbols = watchlist.Items.Select(item => item.Symbol).ToList();
            
            // Fetch all prices in bulk
            var bulkPrices = await GetBulkPricesAsync(symbols);
            var priceDict = bulkPrices.Prices.ToDictionary(p => p.Symbol, p => p);

            // Create response with enriched data
            var response = new WatchlistWithPricesDto
            {
                Id = watchlist.Id,
                Name = watchlist.Name,
                UserId = userId,
                CreatedAt = watchlist.CreatedAt,
                UpdatedAt = watchlist.UpdatedAt,
                IsGlobal = watchlist.IsGlobal,
                TotalItems = watchlist.Items.Count,
                PricesLastUpdated = DateTime.UtcNow
            };

            // Enrich watchlist items with price data
            foreach (var item in watchlist.Items)
            {
                var enrichedItem = new WatchlistItemWithPriceDto
                {
                    Id = item.Id,
                    Symbol = item.Symbol,
                    AddedAt = item.AddedAt,
                    SortOrder = item.SortOrder
                };

                if (priceDict.TryGetValue(item.Symbol, out var priceData))
                {
                    enrichedItem.CurrentPrice = priceData.CurrentPrice;
                    enrichedItem.PreviousPrice = priceData.PreviousPrice;
                    enrichedItem.PriceChange = priceData.PriceChange;
                    enrichedItem.PriceChangePercent = priceData.PriceChangePercent;
                    enrichedItem.LastUpdated = priceData.LastUpdated;
                    enrichedItem.Status = priceData.Status ?? "success";
                }
                else
                {
                    enrichedItem.Status = "error";
                }

                response.Items.Add(enrichedItem);
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting watchlist with prices for watchlist {WatchlistId}", watchlistId);
            throw;
        }
    }

    public async Task<List<PriceAnalyticsDto>> GetPriceAnalyticsAsync(List<string> symbols, string? brokerId = null)
    {
        var analytics = new List<PriceAnalyticsDto>();
        
        // Get current prices
        var currentPrices = await GetBulkPricesAsync(symbols, brokerId);
        
        foreach (var price in currentPrices.Prices)
        {
            var analytic = new PriceAnalyticsDto
            {
                Symbol = price.Symbol,
                CurrentPrice = price.CurrentPrice,
                DayChange = price.PriceChange,
                DayChangePercent = price.PriceChangePercent,
                Volume = price.Volume,
                LastUpdated = price.LastUpdated
            };

            // TODO: Add historical analytics (week/month changes, 52-week high/low)
            // This would require historical price data storage
            
            analytics.Add(analytic);
        }

        return analytics;
    }

    private async Task<SymbolPriceDto?> FetchSinglePriceAsync(string symbol, string? brokerId)
    {
        try
        {
            var priceData = await _priceService.GetLatestPriceAsync(symbol, brokerId);
            if (priceData == null) return null;

            return new SymbolPriceDto
            {
                Symbol = symbol,
                CurrentPrice = priceData.Price,
                PreviousPrice = null, // TODO: Implement previous close tracking
                PriceChange = null, // TODO: Calculate from previous close
                PriceChangePercent = null, // TODO: Calculate from previous close
                Volume = null, // TODO: Add volume to MarketData model
                LastUpdated = priceData.Timestamp,
                Status = "success",
                BrokerId = brokerId
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to fetch price for symbol {Symbol}", symbol);
            return new SymbolPriceDto
            {
                Symbol = symbol,
                Status = "error",
                BrokerId = brokerId,
                LastUpdated = DateTime.UtcNow
            };
        }
    }

    private async Task EnrichWithHistoricalDataAsync(SymbolPriceDto price, string? brokerId)
    {
        // TODO: Implement historical data enrichment
        // This would fetch historical prices and calculate additional metrics
        await Task.CompletedTask;
    }
}
