using MarketDataService.Interfaces;

namespace MarketDataService.Services;

public class BrokerRegistry : IBrokerRegistry
{
    private readonly List<IPriceProvider> _providers;

    public BrokerRegistry(IEnumerable<IPriceProvider> providers)
    {
        _providers = providers.ToList();
    }

    public IPriceProvider? GetProvider(string brokerId) =>
        _providers.FirstOrDefault(p => p.BrokerId == brokerId);

    public IEnumerable<IPriceProvider> GetAllProviders() => _providers;

    public IEnumerable<string> GetAvailableBrokerIds() =>
        _providers.Select(p => p.BrokerId);
}
