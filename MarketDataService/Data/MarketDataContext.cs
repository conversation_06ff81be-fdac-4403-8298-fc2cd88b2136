using MarketDataService.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace MarketDataService.Data;

public class MarketDataContext(DbContextOptions<MarketDataContext> options) : DbContext(options)
{
    public DbSet<MarketData> MarketDataEntries => Set<MarketData>();
    public DbSet<Watchlist> Watchlists => Set<Watchlist>();
    public DbSet<WatchlistItem> WatchlistItems => Set<WatchlistItem>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Watchlist - use existing Supabase table
        modelBuilder.Entity<Watchlist>(entity =>
        {
            entity.ToTable("watchlists");
            entity.HasKey(w => w.Id);
            entity.Property(w => w.Id).HasColumnName("id").HasColumnType("bigint").ValueGeneratedOnAdd();
            entity.Property(w => w.UserId).HasColumnName("user_id").HasColumnType("uuid").IsRequired(false);
            entity.Property(w => w.Name).IsRequired().HasColumnName("name").HasColumnType("text");
            entity.Property(w => w.CreatedAt).HasColumnName("created_at").HasColumnType("timestamp with time zone")
                .IsRequired(false).HasDefaultValueSql("now()");
            entity.Property(w => w.UpdatedAt).HasColumnName("updated_at").HasColumnType("timestamp with time zone")
                .IsRequired(false).HasDefaultValueSql("now()");
            entity.Property(w => w.IsGlobal).HasColumnName("is_global").HasColumnType("boolean")
                .IsRequired(false).HasDefaultValue(false);
            entity.HasIndex(w => new { w.UserId, w.Name }).IsUnique();
            entity.HasIndex(w => new { w.UserId, w.IsGlobal });
        });

        // Configure WatchlistItem - use existing Supabase table
        modelBuilder.Entity<WatchlistItem>(entity =>
        {
            entity.ToTable("watchlist_symbols");
            entity.HasKey(wi => wi.Id);
            entity.Property(wi => wi.Id).HasColumnName("id").HasColumnType("bigint").ValueGeneratedOnAdd();
            entity.Property(wi => wi.WatchlistId).HasColumnName("watchlist_id").HasColumnType("bigint")
                .IsRequired(false);
            entity.Property(wi => wi.Symbol).IsRequired().HasColumnName("symbol").HasColumnType("text");
            entity.Property(wi => wi.Broker).HasColumnName("broker").HasColumnType("text")
                .IsRequired(false);
            entity.Property(wi => wi.AddedAt).HasColumnName("added_at").HasColumnType("timestamp with time zone")
                .IsRequired(false).HasDefaultValueSql("now()");
            entity.Property(wi => wi.UserId).HasColumnName("user_id").HasColumnType("uuid").IsRequired(false);
            entity.Ignore(wi => wi.SortOrder); // SortOrder is only used for display purposes, not stored in DB
            entity.HasIndex(wi => new { wi.WatchlistId, wi.Symbol }).IsUnique();

            entity.HasOne(wi => wi.Watchlist)
                  .WithMany(w => w.Items)
                  .HasForeignKey(wi => wi.WatchlistId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure MarketData
        modelBuilder.Entity<MarketData>(entity =>
        {
            entity.HasKey(md => md.Id);
            entity.Property(md => md.Symbol).IsRequired().HasMaxLength(20);
            entity.HasIndex(md => new { md.Symbol, md.Timestamp });
        });
    }
}