# SSL Configuration Guide for Abra API

This guide explains how to configure SSL/HTTPS for your Abra API Gateway using nginx with Let's Encrypt certificates.

## 🎯 Quick Start

### Production SSL with Let's Encrypt

1. **Update the setup script with your email:**
   ```bash
   # Edit setup-ssl.sh and change the email
   EMAIL="<EMAIL>"
   ```

2. **Make the script executable:**
   ```bash
   chmod +x setup-ssl.sh
   ```

3. **Run the SSL setup:**
   ```bash
   ./setup-ssl.sh setup
   ```

4. **Start your services:**
   ```bash
   docker-compose -f docker-compose.ssl.yml up -d
   ```

## 📋 Configuration Files

### Production Files
- `docker-compose.ssl.yml` - Docker Compose with SSL support
- `nginx/nginx.conf` - Production nginx config with Let's Encrypt
- `setup-ssl.sh` - Automated SSL setup script

## 🔧 Manual SSL Setup

### Step 1: Domain Configuration
Ensure your domain `abraapp.undeclab.com` points to your server's IP address.

### Step 2: Let's Encrypt Certificate
```bash
# Install certbot (if not using Docker)
sudo apt update
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot certonly --webroot \
  --webroot-path=/var/www/certbot \
  --email <EMAIL> \
  --agree-tos \
  --no-eff-email \
  -d abraapp.undeclab.com
```

### Step 3: Nginx Configuration
The nginx configuration includes:
- HTTP to HTTPS redirect
- SSL certificate paths
- Security headers (HSTS, etc.)
- Proper SSL protocols and ciphers

### Step 4: Auto-Renewal
Set up automatic certificate renewal:
```bash
# Add to crontab
0 12 * * * /path/to/your/project/renew-ssl.sh
```

## 🛡️ Security Features

### SSL Configuration
- **TLS 1.2 and 1.3** support only
- **Strong cipher suites** for security
- **HSTS** (HTTP Strict Transport Security)
- **Perfect Forward Secrecy**

### Security Headers
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Strict-Transport-Security` (HSTS)

## 🧪 Testing SSL

### Test Certificate
```bash
# Check certificate details
openssl s_client -connect abraapp.undeclab.com:443 -servername abraapp.undeclab.com

# Test SSL configuration
curl -I https://abraapp.undeclab.com/health
```

### SSL Labs Test
Visit [SSL Labs](https://www.ssllabs.com/ssltest/) and test your domain for security rating.

## 🔄 Certificate Management

### Renewal
```bash
# Manual renewal
./setup-ssl.sh renew

# Check certificate expiry
openssl x509 -in /etc/letsencrypt/live/abraapp.undeclab.com/cert.pem -noout -dates
```

### Troubleshooting
```bash
# Check nginx configuration
./setup-ssl.sh test

# View nginx logs
docker-compose -f docker-compose.ssl.yml logs nginx

# Check certificate status
docker-compose -f docker-compose.ssl.yml run --rm certbot certificates
```

## 🚀 Production Deployment

### Environment Variables
Create a `.env` file with:
```env
SUPABASE_CONNECTION_STRING=your_connection_string
FINNHUB_API_KEY=your_finnhub_key
POLYGON_API_KEY=your_polygon_key
REDIS_CONNECTION_STRING=redis:6379
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_key
OLLAMA_BASE_URL=your_ollama_url
```

### Firewall Configuration
```bash
# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
```

### Monitoring
- Monitor certificate expiry dates
- Set up alerts for SSL certificate renewal failures
- Monitor SSL Labs rating periodically

## 📚 Additional Resources

- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
- [nginx SSL Configuration](https://nginx.org/en/docs/http/configuring_https_servers.html)
- [Mozilla SSL Configuration Generator](https://ssl-config.mozilla.org/)
- [SSL Labs Server Test](https://www.ssllabs.com/ssltest/)

## 🆘 Common Issues

### Certificate Not Found
- Ensure domain DNS is properly configured
- Check that port 80 is accessible for Let's Encrypt validation
- Verify nginx is running and serving the challenge files

### Mixed Content Warnings
- Ensure all API calls use HTTPS
- Update any hardcoded HTTP URLs to HTTPS
- Check that proxy headers are properly set

### Certificate Renewal Failures
- Check disk space in `/etc/letsencrypt/`
- Ensure nginx is running during renewal
- Verify domain is still pointing to your server
