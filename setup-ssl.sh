#!/bin/bash

# SSL Setup Script for Abra API
# This script helps set up SSL certificates using Let's Encrypt

set -e

DOMAIN="abraapp.undeclab.com"
EMAIL="<EMAIL>"  # Change this to your email

echo "🔐 Setting up SSL for $DOMAIN"

# Create directories for certbot
echo "📁 Creating directories..."
mkdir -p ./certbot/www
mkdir -p ./certbot/conf

# Function to get certificates
get_certificate() {
    echo "🔄 Requesting SSL certificate..."
    docker-compose -f docker-compose.ssl.yml run --rm certbot \
        certonly --webroot \
        --webroot-path=/var/www/certbot \
        --email $EMAIL \
        --agree-tos \
        --no-eff-email \
        -d $DOMAIN
}

# Function to renew certificates
renew_certificate() {
    echo "🔄 Renewing SSL certificates..."
    docker-compose -f docker-compose.ssl.yml run --rm certbot renew
}

# Function to test nginx configuration
test_nginx() {
    echo "🧪 Testing nginx configuration..."
    docker-compose -f docker-compose.ssl.yml exec nginx nginx -t
}

# Function to reload nginx
reload_nginx() {
    echo "🔄 Reloading nginx..."
    docker-compose -f docker-compose.ssl.yml exec nginx nginx -s reload
}

# Main setup function
setup_ssl() {
    echo "🚀 Starting SSL setup process..."
    
    # Step 1: Start services without SSL first
    echo "1️⃣ Starting services for certificate validation..."
    docker-compose -f docker-compose.ssl.yml up -d nginx
    
    # Wait a bit for nginx to start
    sleep 5
    
    # Step 2: Get certificate
    echo "2️⃣ Requesting SSL certificate..."
    get_certificate
    
    # Step 3: Restart nginx with SSL
    echo "3️⃣ Restarting nginx with SSL configuration..."
    docker-compose -f docker-compose.ssl.yml restart nginx
    
    # Step 4: Test configuration
    echo "4️⃣ Testing SSL configuration..."
    sleep 5
    curl -I https://$DOMAIN/health || echo "⚠️  SSL test failed - check configuration"
    
    echo "✅ SSL setup complete!"
    echo "🌐 Your API should now be available at: https://$DOMAIN"
}

# Function to create a cron job for certificate renewal
setup_auto_renewal() {
    echo "⏰ Setting up automatic certificate renewal..."
    
    # Create renewal script
    cat > ./renew-ssl.sh << 'EOF'
#!/bin/bash
cd /path/to/your/project  # Update this path
docker-compose -f docker-compose.ssl.yml run --rm certbot renew
docker-compose -f docker-compose.ssl.yml exec nginx nginx -s reload
EOF
    
    chmod +x ./renew-ssl.sh
    
    echo "📝 Add this to your crontab (run 'crontab -e'):"
    echo "0 12 * * * /path/to/your/project/renew-ssl.sh"
}

# Parse command line arguments
case "${1:-setup}" in
    "setup")
        setup_ssl
        ;;
    "renew")
        renew_certificate
        reload_nginx
        ;;
    "test")
        test_nginx
        ;;
    "auto-renewal")
        setup_auto_renewal
        ;;
    *)
        echo "Usage: $0 {setup|renew|test|auto-renewal}"
        echo ""
        echo "Commands:"
        echo "  setup        - Initial SSL setup with Let's Encrypt"
        echo "  renew        - Renew existing certificates"
        echo "  test         - Test nginx configuration"
        echo "  auto-renewal - Setup automatic certificate renewal"
        exit 1
        ;;
esac
