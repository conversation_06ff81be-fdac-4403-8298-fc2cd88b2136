namespace AssistantService.Services;

public interface IPromptInjectionDetector
{
    /// <summary>
    /// Analyzes input for potential prompt injection attacks
    /// </summary>
    /// <param name="input">The user input to analyze</param>
    /// <returns>Detection result with risk level and details</returns>
    PromptInjectionResult AnalyzeInput(string input);

    /// <summary>
    /// Sanitizes input by removing or neutralizing potential injection attempts
    /// </summary>
    /// <param name="input">The input to sanitize</param>
    /// <returns>Sanitized input safe for processing</returns>
    string SanitizeInput(string input);
}

public class PromptInjectionResult
{
    public bool IsInjectionDetected { get; set; }
    public RiskLevel RiskLevel { get; set; }
    public List<string> DetectedPatterns { get; set; } = new();
    public string? RecommendedAction { get; set; }
    public string? SanitizedInput { get; set; }
}

public enum RiskLevel
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}
