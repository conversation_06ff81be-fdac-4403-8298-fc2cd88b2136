using Microsoft.AspNetCore.Mvc;
using AssistantService.Models;
using AssistantService.Services;
using System.Text.Json;
using System.Text;

namespace AssistantService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ChatController(IOllamaClient ollama, IPromptInjectionDetector injectionDetector, IContentFilter contentFilter) : ControllerBase
{
    private readonly IOllamaClient _ollama = ollama;
    private readonly IPromptInjectionDetector _injectionDetector = injectionDetector;
    private readonly IContentFilter _contentFilter = contentFilter;

    [HttpPost]
    public async Task<IActionResult> Chat([FromBody] ChatRequest request)
    {
        // Validate input
        if (request == null)
        {
            return BadRequest("Request cannot be empty");
        }

        if (string.IsNullOrWhiteSpace(request.Model))
        {
            return BadRequest("Model is required");
        }

        if (request.Messages == null || request.Messages.Count == 0)
        {
            return BadRequest("At least one message is required");
        }

        // Get the last user message
        var userMessage = request.Messages.LastOrDefault(m => m.Role.Equals("user", StringComparison.OrdinalIgnoreCase));
        if (userMessage == null)
        {
            return BadRequest("No user message found");
        }

        // Check for prompt injection attacks
        var injectionResult = _injectionDetector.AnalyzeInput(userMessage.Content);

        if (injectionResult.RiskLevel == RiskLevel.Critical)
        {
            return BadRequest("Request blocked due to security concerns");
        }

        if (injectionResult.RiskLevel == RiskLevel.High)
        {
            return BadRequest("Request contains potentially harmful content");
        }

        // Apply content filtering
        var contentResult = _contentFilter.FilterContent(injectionResult.SanitizedInput ?? userMessage.Content);

        if (!contentResult.IsContentSafe)
        {
            return BadRequest($"Content policy violation: {contentResult.Reason}");
        }

        // Use filtered content
        var sanitizedContent = contentResult.FilteredContent ?? string.Empty;

        if (string.IsNullOrWhiteSpace(sanitizedContent))
        {
            return BadRequest("Message content cannot be empty");
        }

        if (sanitizedContent.Length > 4000)
        {
            return BadRequest("Message content is too long (max 4000 characters)");
        }

        try
        {
            if (request.Stream)
            {
                return await StreamResponse(sanitizedContent);
            }
            else
            {
                return await NonStreamResponse(sanitizedContent);
            }
        }
        catch (InvalidOperationException ex)
        {
            return StatusCode(503, ex.Message);
        }
        catch (Exception)
        {
            return StatusCode(500, "Something went wrong. Please try again.");
        }
    }

    private async Task<IActionResult> StreamResponse(string content)
    {
        Response.Headers.ContentType = "text/plain; charset=utf-8";
        Response.Headers.CacheControl = "no-cache";
        Response.Headers.Connection = "keep-alive";

        var reply = await _ollama.AskAsync(content);
        
        // Simulate streaming by breaking the response into chunks
        const int chunkSize = 5; // Characters per chunk
        const int delayMs = 50; // Milliseconds between chunks

        await Response.StartAsync();

        for (int i = 0; i < reply.Length; i += chunkSize)
        {
            var end = Math.Min(i + chunkSize, reply.Length);
            var chunk = reply.Substring(i, end - i);
            var isDone = end >= reply.Length;

            var streamResponse = new StreamingChatResponse
            {
                Message = new ChatMessage
                {
                    Role = "assistant",
                    Content = chunk
                },
                Done = isDone
            };

            var jsonChunk = JsonSerializer.Serialize(streamResponse) + "\n";
            var bytes = Encoding.UTF8.GetBytes(jsonChunk);
            
            await Response.Body.WriteAsync(bytes);
            await Response.Body.FlushAsync();

            // Add delay between chunks (except for the last one)
            if (!isDone)
            {
                await Task.Delay(delayMs);
            }
        }

        return new EmptyResult();
    }

    private async Task<IActionResult> NonStreamResponse(string content)
    {
        var reply = await _ollama.AskAsync(content);
        
        var response = new ChatResponse
        {
            Message = new ChatMessage
            {
                Role = "assistant",
                Content = reply
            },
            Done = true
        };

        return Ok(response);
    }


}
