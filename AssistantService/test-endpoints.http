@assistant_service_HostAddress = http://localhost:5123

### Test the original /api/assistant/ask endpoint
POST {{assistant_service_HostAddress}}/api/assistant/ask
Content-Type: application/json

{
  "prompt": "What is artificial intelligence?"
}

### Test the new /api/chat endpoint (non-streaming)
POST {{assistant_service_HostAddress}}/api/chat
Content-Type: application/json

{
  "model": "mistral",
  "messages": [
    {
      "role": "user",
      "content": "What is artificial intelligence?"
    }
  ],
  "stream": false
}

### Test the new /api/chat endpoint (streaming)
POST {{assistant_service_HostAddress}}/api/chat
Content-Type: application/json

{
  "model": "mistral",
  "messages": [
    {
      "role": "user",
      "content": "Tell me a short joke"
    }
  ],
  "stream": true
}

### Test chat endpoint with conversation history
POST {{assistant_service_HostAddress}}/api/chat
Content-Type: application/json

{
  "model": "mistral",
  "messages": [
    {
      "role": "user",
      "content": "What is 2+2?"
    },
    {
      "role": "assistant", 
      "content": "2+2 equals 4."
    },
    {
      "role": "user",
      "content": "What about 3+3?"
    }
  ],
  "stream": false
}

### Test error handling - empty request
POST {{assistant_service_HostAddress}}/api/chat
Content-Type: application/json

{}

### Test error handling - missing model
POST {{assistant_service_HostAddress}}/api/chat
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "Test"
    }
  ]
}

### Test error handling - no messages
POST {{assistant_service_HostAddress}}/api/chat
Content-Type: application/json

{
  "model": "mistral",
  "messages": []
}
