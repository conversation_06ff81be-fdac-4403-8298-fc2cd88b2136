#!/bin/bash

# Rollback and recovery script for Abra API
# Provides automatic rollback capabilities and disaster recovery

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CURRENT_ENV_FILE="$SCRIPT_DIR/.current_environment"
DEPLOYMENT_HISTORY_FILE="$SCRIPT_DIR/.deployment_history"
RECOVERY_LOG="$SCRIPT_DIR/recovery.log"
BACKUP_DIR="$SCRIPT_DIR/backups"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$RECOVERY_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$RECOVERY_LOG"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$RECOVERY_LOG"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$RECOVERY_LOG"
}

# Get current active environment
get_current_environment() {
    if [[ -f "$CURRENT_ENV_FILE" ]]; then
        cat "$CURRENT_ENV_FILE"
    else
        echo "blue"  # Default to blue
    fi
}

# Record deployment in history
record_deployment() {
    local environment=$1
    local status=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local commit_hash=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    mkdir -p "$(dirname "$DEPLOYMENT_HISTORY_FILE")"
    echo "$timestamp,$environment,$status,$commit_hash" >> "$DEPLOYMENT_HISTORY_FILE"
}

# Get last successful deployment
get_last_successful_deployment() {
    if [[ -f "$DEPLOYMENT_HISTORY_FILE" ]]; then
        grep ",success," "$DEPLOYMENT_HISTORY_FILE" | tail -1 | cut -d',' -f2
    else
        echo ""
    fi
}

# Create backup of current state
create_backup() {
    local environment=$1
    local backup_name="backup-$environment-$(date +%Y%m%d-%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    log "Creating backup: $backup_name"
    mkdir -p "$backup_path"
    
    # Backup environment file
    cp "$CURRENT_ENV_FILE" "$backup_path/" 2>/dev/null || true
    
    # Backup nginx configuration
    cp -r nginx/ "$backup_path/" 2>/dev/null || true
    
    # Backup Docker Compose files
    cp docker-compose.*.yml "$backup_path/" 2>/dev/null || true
    
    # Export Docker images
    log "Exporting Docker images for $environment environment..."
    local services=("assistant-service-$environment" "auth-service-$environment" "marketdata-service-$environment" "thread-service-$environment")
    
    for service in "${services[@]}"; do
        if docker images | grep -q "$service"; then
            docker save "$service" | gzip > "$backup_path/$service.tar.gz" 2>/dev/null || warning "Failed to backup $service image"
        fi
    done
    
    # Create backup manifest
    {
        echo "Backup created: $(date)"
        echo "Environment: $environment"
        echo "Git commit: $(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
        echo "Services backed up:"
        ls -la "$backup_path/"
    } > "$backup_path/manifest.txt"
    
    success "Backup created: $backup_path"
    echo "$backup_path"
}

# Restore from backup
restore_from_backup() {
    local backup_path=$1
    
    if [[ ! -d "$backup_path" ]]; then
        error "Backup path does not exist: $backup_path"
        return 1
    fi
    
    log "Restoring from backup: $backup_path"
    
    # Stop all services
    log "Stopping all services..."
    docker compose -f docker-compose.blue.yml down --remove-orphans || true
    docker compose -f docker-compose.green.yml down --remove-orphans || true
    
    # Restore configuration files
    if [[ -f "$backup_path/.current_environment" ]]; then
        cp "$backup_path/.current_environment" "$CURRENT_ENV_FILE"
        log "Restored environment configuration"
    fi
    
    if [[ -d "$backup_path/nginx" ]]; then
        cp -r "$backup_path/nginx/"* nginx/
        log "Restored nginx configuration"
    fi
    
    # Restore Docker images
    log "Restoring Docker images..."
    for image_file in "$backup_path"/*.tar.gz; do
        if [[ -f "$image_file" ]]; then
            log "Loading $(basename "$image_file")..."
            gunzip -c "$image_file" | docker load || warning "Failed to load $(basename "$image_file")"
        fi
    done
    
    # Start services
    local environment=$(get_current_environment)
    log "Starting $environment environment..."
    docker compose -f "docker-compose.$environment.yml" up -d
    
    success "Restore completed from backup: $backup_path"
}

# Quick rollback to previous environment
quick_rollback() {
    local current_env=$(get_current_environment)
    local target_env
    
    if [[ "$current_env" == "blue" ]]; then
        target_env="green"
    else
        target_env="blue"
    fi
    
    log "Performing quick rollback from $current_env to $target_env..."
    
    # Create backup of current state before rollback
    local backup_path=$(create_backup "$current_env")
    
    # Check if target environment containers exist and are healthy
    if docker compose -f "docker-compose.$target_env.yml" ps | grep -q "Up"; then
        log "Target environment containers are running, switching traffic..."
        
        # Switch nginx upstream
        cp "nginx/upstreams.$target_env.conf" "nginx/upstreams.conf"
        
        if docker exec nginx-proxy nginx -t && docker exec nginx-proxy nginx -s reload; then
            echo "$target_env" > "$CURRENT_ENV_FILE"
            record_deployment "$target_env" "rollback"
            success "Quick rollback completed successfully"
            return 0
        else
            error "Failed to reload nginx configuration"
            return 1
        fi
    else
        log "Target environment not running, starting services..."
        
        # Start target environment
        docker compose -f "docker-compose.$target_env.yml" up -d
        
        # Wait for services to be ready
        sleep 60
        
        # Health check
        if ./health-monitor.sh check "$target_env"; then
            # Switch traffic
            cp "nginx/upstreams.$target_env.conf" "nginx/upstreams.conf"
            docker exec nginx-proxy nginx -s reload
            echo "$target_env" > "$CURRENT_ENV_FILE"
            record_deployment "$target_env" "rollback"
            success "Rollback with service restart completed successfully"
            return 0
        else
            error "Health check failed for target environment"
            return 1
        fi
    fi
}

# Emergency recovery - restore to last known good state
emergency_recovery() {
    log "Initiating emergency recovery..."
    
    # Stop all services
    log "Stopping all services..."
    docker compose -f docker-compose.blue.yml down --remove-orphans || true
    docker compose -f docker-compose.green.yml down --remove-orphans || true
    docker compose -f docker-compose.yml down --remove-orphans || true
    docker compose -f docker-compose.ssl.yml down --remove-orphans || true
    
    # Clean up Docker resources
    log "Cleaning up Docker resources..."
    docker system prune -f || true
    
    # Find the most recent backup
    local latest_backup
    if [[ -d "$BACKUP_DIR" ]]; then
        latest_backup=$(ls -t "$BACKUP_DIR" | head -1)
        if [[ -n "$latest_backup" ]]; then
            log "Found latest backup: $latest_backup"
            restore_from_backup "$BACKUP_DIR/$latest_backup"
            return $?
        fi
    fi
    
    # If no backup available, try to restore to a basic working state
    log "No backup found, attempting basic recovery..."
    
    # Reset to blue environment
    echo "blue" > "$CURRENT_ENV_FILE"
    cp "nginx/upstreams.blue.conf" "nginx/upstreams.conf" 2>/dev/null || true
    
    # Start basic services
    docker compose -f docker-compose.blue.yml up -d
    
    # Wait and check
    sleep 120
    if ./health-monitor.sh check blue; then
        success "Basic recovery completed"
        record_deployment "blue" "emergency_recovery"
        return 0
    else
        error "Emergency recovery failed"
        return 1
    fi
}

# Automated recovery based on health checks
auto_recovery() {
    local max_attempts=${1:-3}
    local current_env=$(get_current_environment)
    
    log "Starting automated recovery (max attempts: $max_attempts)..."
    
    for ((attempt=1; attempt<=max_attempts; attempt++)); do
        log "Recovery attempt $attempt/$max_attempts"
        
        # Check current environment health
        if ./health-monitor.sh check "$current_env"; then
            success "Current environment is healthy, no recovery needed"
            return 0
        fi
        
        # Try quick rollback first
        if [[ $attempt -eq 1 ]]; then
            log "Attempting quick rollback..."
            if quick_rollback; then
                success "Quick rollback successful"
                return 0
            fi
        fi
        
        # Try restarting current environment
        log "Attempting to restart current environment..."
        docker compose -f "docker-compose.$current_env.yml" restart
        sleep 60
        
        if ./health-monitor.sh check "$current_env"; then
            success "Service restart successful"
            return 0
        fi
        
        # If this is the last attempt, try emergency recovery
        if [[ $attempt -eq $max_attempts ]]; then
            log "All attempts failed, initiating emergency recovery..."
            emergency_recovery
            return $?
        fi
        
        log "Attempt $attempt failed, waiting before next attempt..."
        sleep 30
    done
    
    error "All recovery attempts failed"
    return 1
}

# List available backups
list_backups() {
    log "Available backups:"
    
    if [[ -d "$BACKUP_DIR" ]]; then
        for backup in "$BACKUP_DIR"/backup-*; do
            if [[ -d "$backup" ]]; then
                echo "  $(basename "$backup")"
                if [[ -f "$backup/manifest.txt" ]]; then
                    sed 's/^/    /' "$backup/manifest.txt"
                fi
                echo
            fi
        done
    else
        log "No backup directory found"
    fi
}

# Show deployment history
show_history() {
    log "Deployment history:"
    
    if [[ -f "$DEPLOYMENT_HISTORY_FILE" ]]; then
        echo "Timestamp,Environment,Status,Commit"
        echo "----------------------------------------"
        tail -20 "$DEPLOYMENT_HISTORY_FILE"
    else
        log "No deployment history found"
    fi
}

# Main script logic
case "${1:-help}" in
    "rollback")
        quick_rollback
        ;;
    "emergency")
        emergency_recovery
        ;;
    "auto")
        max_attempts=${2:-3}
        auto_recovery "$max_attempts"
        ;;
    "backup")
        env=${2:-$(get_current_environment)}
        create_backup "$env"
        ;;
    "restore")
        if [[ -z "$2" ]]; then
            error "Please specify backup path"
            exit 1
        fi
        restore_from_backup "$2"
        ;;
    "list-backups")
        list_backups
        ;;
    "history")
        show_history
        ;;
    *)
        echo "Usage: $0 {rollback|emergency|auto|backup|restore|list-backups|history} [options]"
        echo "  rollback           - Quick rollback to previous environment"
        echo "  emergency          - Emergency recovery to last known good state"
        echo "  auto [attempts]    - Automated recovery with multiple strategies (default 3 attempts)"
        echo "  backup [env]       - Create backup of specified environment"
        echo "  restore <path>     - Restore from backup"
        echo "  list-backups       - List available backups"
        echo "  history            - Show deployment history"
        exit 1
        ;;
esac
