name: Initial SSL Setup

on:
  workflow_dispatch:  # Manual trigger only
    inputs:
      email:
        description: 'Email for Let\'s Encrypt registration'
        required: true
        type: string
      domain:
        description: 'Domain name'
        required: false
        default: 'abraapp.undeclab.com'
        type: string

jobs:
  setup-ssl:
    runs-on: ubuntu-latest
    timeout-minutes: 45

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Initial SSL Setup
      uses: appleboy/ssh-action@v1.2.2
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: 22
        timeout: 2700s  # 45 minutes
        command_timeout: 2700s
        script: |
          set -e
          
          echo "🔐 Starting initial SSL setup for ${{ github.event.inputs.domain }}..."
          cd ~/abraapi

          # Update repository to get latest SSL configuration
          echo "📥 Updating repository..."
          git fetch origin --depth=1
          git reset --hard origin/main

          # Create required directories
          mkdir -p certbot/www
          mkdir -p certbot/conf
          mkdir -p nginx

          # Configure SSL setup script
          echo "🔧 Configuring SSL setup..."
          
          # Update email in setup script
          sed -i 's/EMAIL="<EMAIL>"/EMAIL="${{ github.event.inputs.email }}"/' setup-ssl.sh
          
          # Update domain if different from default
          if [ "${{ github.event.inputs.domain }}" != "abraapp.undeclab.com" ]; then
            sed -i 's/DOMAIN="abraapp.undeclab.com"/DOMAIN="${{ github.event.inputs.domain }}"/' setup-ssl.sh
            sed -i 's/abraapp.undeclab.com/${{ github.event.inputs.domain }}/g' nginx/nginx.conf
            sed -i 's/abraapp.undeclab.com/${{ github.event.inputs.domain }}/g' docker-compose.ssl.yml
          fi
          
          # Make script executable
          chmod +x setup-ssl.sh

          # Create environment file with SSL configuration
          echo "🔧 Creating environment configuration..."
          cat > .env << EOF
          # Database
          POSTGRES_USER=${{ secrets.POSTGRES_USER }}
          POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}
          POSTGRES_DB=${{ secrets.POSTGRES_DB }}
          REDIS_CONNECTION_STRING=${{ secrets.REDIS_CONNECTION_STRING }}

          # APIs
          FINNHUB_API_KEY=${{ secrets.FINNHUB_API_KEY }}
          POLYGON_API_KEY=${{ secrets.POLYGON_API_KEY }}
          ASPNETCORE_ENVIRONMENT=${{ secrets.ASPNETCORE_ENVIRONMENT }}

          # Supabase
          SUPABASE_JWT_SECRET=${{ secrets.SUPABASE_JWT_SECRET }}
          SUPABASE_URL=${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          SUPABASE_PROJECT_ID=${{ secrets.SUPABASE_PROJECT_ID }}
          SUPABASE_CONNECTION_STRING=${{ secrets.SUPABASE_CONNECTION_STRING }}

          # Ollama
          OLLAMA_URL=${{ secrets.OLLAMA_URL }}

          # SSL Configuration
          SSL_EMAIL=${{ github.event.inputs.email }}
          DOMAIN=${{ github.event.inputs.domain }}
          EOF

          # Stop any existing services
          echo "🛑 Stopping existing services..."
          docker compose down --remove-orphans || true
          docker compose -f docker-compose.ssl.yml down --remove-orphans || true

          # Clean up Docker resources
          docker system prune -f || true

          # Build services
          echo "🔨 Building services..."
          docker compose -f docker-compose.ssl.yml build --no-cache

          # Run SSL setup
          echo "🔐 Setting up SSL certificates..."
          if ./setup-ssl.sh setup; then
            echo "✅ SSL setup completed successfully"
            
            # Test SSL configuration
            echo "🧪 Testing SSL configuration..."
            sleep 30
            
            if curl -I https://${{ github.event.inputs.domain }}/health --connect-timeout 30; then
              echo "✅ HTTPS is working correctly"
            else
              echo "⚠️ HTTPS test failed, but certificates may still be valid"
            fi
            
            if curl -I http://${{ github.event.inputs.domain }}/health --connect-timeout 10; then
              echo "✅ HTTP redirect is working"
            else
              echo "⚠️ HTTP test failed"
            fi
            
          else
            echo "❌ SSL setup failed"
            echo "📋 Showing nginx logs for debugging..."
            docker compose -f docker-compose.ssl.yml logs nginx || true
            exit 1
          fi

          # Show final status
          echo "📊 Final service status:"
          docker compose -f docker-compose.ssl.yml ps

          echo "✅ SSL setup completed successfully!"
          echo "🌐 Your API is now available at:"
          echo "  - HTTPS: https://${{ github.event.inputs.domain }}"
          echo "  - HTTP:  http://${{ github.event.inputs.domain }} (redirects to HTTPS)"
          echo ""
          echo "🔐 SSL certificate information:"
          openssl x509 -in certbot/conf/live/${{ github.event.inputs.domain }}/fullchain.pem -noout -dates || echo "Could not read certificate dates"

    - name: Setup Auto-Renewal
      if: success()
      uses: appleboy/ssh-action@v1.2.2
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: 22
        script: |
          cd ~/abraapi
          
          echo "⏰ Setting up automatic SSL renewal..."
          
          # Create renewal script
          cat > renew-ssl-cron.sh << 'EOF'
          #!/bin/bash
          cd ~/abraapi
          ./setup-ssl.sh renew
          EOF
          
          chmod +x renew-ssl-cron.sh
          
          # Add to crontab (run twice daily)
          (crontab -l 2>/dev/null; echo "0 2,14 * * * ~/abraapi/renew-ssl-cron.sh >> ~/ssl-renewal.log 2>&1") | crontab -
          
          echo "✅ Auto-renewal configured to run twice daily"
          echo "📝 Renewal logs will be saved to ~/ssl-renewal.log"
