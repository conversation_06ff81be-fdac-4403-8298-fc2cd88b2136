name: Build and Deploy with SSL to GCE

on:
  push:
    branches: [main]
  workflow_dispatch:  # Allow manual triggers
    inputs:
      setup_ssl:
        description: 'Setup SSL certificates'
        required: false
        default: 'false'
        type: boolean
      force_ssl_renewal:
        description: 'Force SSL certificate renewal'
        required: false
        default: 'false'
        type: boolean

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 60  # Increased for SSL setup

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3.11.1
      with:
        driver-opts: |
          network=host

    - name: Update Docker Compose
      run: |
        # Ensure we have a recent version of Docker Compose
        sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
        docker-compose --version || echo "docker-compose not available, using docker compose"
          
    - name: Fast Deploy to VM via SSH
      uses: appleboy/ssh-action@v1.2.2
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: 22
        timeout: 2700s  # 15 minutes
        command_timeout: 2700s
        script: |
          set -e  # Exit on any error
          
          echo "🚀 Starting fast deployment process..."
          cd ~/abraapi

          # Quick git update
          echo "📥 Updating repository..."
          git fetch origin --depth=1
          git reset --hard origin/main
          echo "✅ Updated to commit: $(git rev-parse --short HEAD)"
          
          # Verify directory structure
          echo "📁 Verifying project structure..."
          ls -la
          echo "📁 AuthService directory:"
          ls -la AuthService/ || echo "AuthService directory not found"

          # Create optimized .env file
          echo "🔧 Creating environment configuration..."
          cat > .env << EOF
          # Database
          POSTGRES_USER=${{ secrets.POSTGRES_USER }}
          POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}
          POSTGRES_DB=${{ secrets.POSTGRES_DB }}
          REDIS_CONNECTION_STRING=${{ secrets.REDIS_CONNECTION_STRING }}

          # APIs
          FINNHUB_API_KEY=${{ secrets.FINNHUB_API_KEY }}
          POLYGON_API_KEY=${{ secrets.POLYGON_API_KEY }}
          ASPNETCORE_ENVIRONMENT=${{ secrets.ASPNETCORE_ENVIRONMENT }}

          # Supabase
          SUPABASE_JWT_SECRET=${{ secrets.SUPABASE_JWT_SECRET }}
          SUPABASE_URL=${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          SUPABASE_PROJECT_ID=${{ secrets.SUPABASE_PROJECT_ID }}
          SUPABASE_CONNECTION_STRING=${{ secrets.SUPABASE_CONNECTION_STRING }}

          # Ollama
          OLLAMA_URL=${{ secrets.OLLAMA_URL }}

          # SSL Configuration
          SSL_EMAIL=${{ secrets.SSL_EMAIL }}
          DOMAIN=abraapp.undeclab.com
          EOF
          
          # Debug: Check if critical environment variables are set
          echo "🔍 Checking critical environment variables..."
          if [ -z "${{ secrets.SUPABASE_CONNECTION_STRING }}" ]; then
            echo "⚠️ WARNING: SUPABASE_CONNECTION_STRING secret is not set!"
          else
            echo "✅ SUPABASE_CONNECTION_STRING is configured"
          fi

          if [ -z "${{ secrets.SUPABASE_URL }}" ]; then
            echo "⚠️ WARNING: SUPABASE_URL secret is not set!"
          else
            echo "✅ SUPABASE_URL is configured"
          fi

          if [ -z "${{ secrets.SUPABASE_JWT_SECRET }}" ]; then
            echo "⚠️ WARNING: SUPABASE_JWT_SECRET secret is not set!"
          else
            echo "✅ SUPABASE_JWT_SECRET is configured"
          fi
          
          # Show .env file structure (without sensitive values)
          echo "📄 Environment file structure:"
          grep -E "^[A-Z_]+" .env | sed 's/=.*/=***/' || echo "No environment variables found"

          # Ensure required directories exist
          mkdir -p nginx
          mkdir -p certbot/www
          mkdir -p certbot/conf

          # Setup SSL if requested or if certificates don't exist
          SSL_SETUP_NEEDED=false
          if [ "${{ github.event.inputs.setup_ssl }}" = "true" ] || [ "${{ github.event.inputs.force_ssl_renewal }}" = "true" ]; then
            SSL_SETUP_NEEDED=true
          elif [ ! -f "certbot/conf/live/abraapp.undeclab.com/fullchain.pem" ]; then
            echo "🔍 SSL certificates not found, will setup SSL"
            SSL_SETUP_NEEDED=true
          fi

          # Configure SSL setup script
          if [ "$SSL_SETUP_NEEDED" = "true" ]; then
            echo "🔐 Configuring SSL setup..."

            # Update SSL email in setup script
            if [ -n "${{ secrets.SSL_EMAIL }}" ]; then
              sed -i 's/EMAIL="<EMAIL>"/EMAIL="${{ secrets.SSL_EMAIL }}"/' setup-ssl.sh
              echo "✅ SSL email configured"
            else
              echo "⚠️ WARNING: SSL_EMAIL secret not set, using default"
            fi

            # Make SSL script executable
            chmod +x setup-ssl.sh
          fi

          # Enable Docker BuildKit for faster builds
          export DOCKER_BUILDKIT=1
          export COMPOSE_DOCKER_CLI_BUILD=1

          # Check system resources
          echo "💾 System resources:"
          df -h
          free -h
          
          # Clean up previous containers and images to free space
          echo "🧹 Cleaning up previous deployment..."
          docker compose down --remove-orphans || true
          docker system prune -f || true

          # Clear Docker build cache to ensure fresh builds
          docker builder prune -f || true
          
          # Clear NuGet cache to fix corrupted packages
          echo "🧹 Clearing NuGet cache..."
          docker run --rm -v $(pwd):/src -w /src mcr.microsoft.com/dotnet/sdk:10.0-preview \
            dotnet nuget locals all --clear || true
          
          # Check Docker status
          echo "🐳 Docker status:"
          docker --version
          docker compose version

          # Determine which Docker Compose file to use
          COMPOSE_FILE="docker-compose.yml"
          if [ "$SSL_SETUP_NEEDED" = "true" ] || [ -f "certbot/conf/live/abraapp.undeclab.com/fullchain.pem" ]; then
            COMPOSE_FILE="docker-compose.ssl.yml"
            echo "🔐 Using SSL-enabled Docker Compose configuration"
          else
            echo "🌐 Using standard Docker Compose configuration"
          fi

          # Build and start services with no cache to ensure fresh packages
          echo "🔨 Building and starting services..."
          docker compose -f $COMPOSE_FILE build --no-cache

          # Handle SSL setup if needed
          if [ "$SSL_SETUP_NEEDED" = "true" ]; then
            echo "🔐 Setting up SSL certificates..."

            # Start nginx first for Let's Encrypt validation
            docker compose -f $COMPOSE_FILE up -d nginx
            sleep 30

            # Setup or renew SSL certificates
            if [ "${{ github.event.inputs.force_ssl_renewal }}" = "true" ]; then
              echo "🔄 Forcing SSL certificate renewal..."
              ./setup-ssl.sh renew || echo "⚠️ SSL renewal failed, continuing with existing certificates"
            else
              echo "🆕 Setting up new SSL certificates..."
              ./setup-ssl.sh setup || echo "⚠️ SSL setup failed, falling back to HTTP"
            fi

            # Restart nginx to load certificates
            docker compose -f $COMPOSE_FILE restart nginx
            sleep 10
          fi

          # Start all services
          docker compose -f $COMPOSE_FILE up -d

          # Wait for services to be healthy
          echo "⏳ Waiting for services to start..."
          sleep 180

          # Check service status
          echo "📊 Service status:"
          docker compose -f $COMPOSE_FILE ps

          # Check for any unhealthy services and show their logs
          echo "🔍 Checking for unhealthy services..."

          # Check for restarting services (exit code 139 indicates segfault/memory issues)
          RESTARTING_COUNT=$(docker compose -f $COMPOSE_FILE ps | grep -c "Restarting" || echo "0")
          UNHEALTHY_COUNT=$(docker compose -f $COMPOSE_FILE ps | grep -c "unhealthy" || echo "0")

          if [ "$RESTARTING_COUNT" -gt 0 ] || [ "$UNHEALTHY_COUNT" -gt 0 ]; then
            echo "⚠️ Found $RESTARTING_COUNT restarting and $UNHEALTHY_COUNT unhealthy services:"
            docker compose -f $COMPOSE_FILE ps

            echo "📋 Showing logs for all services..."
            docker compose -f $COMPOSE_FILE logs --tail=100 thread-service || true
            docker compose -f $COMPOSE_FILE logs --tail=100 auth-service || true
            docker compose -f $COMPOSE_FILE logs --tail=100 assistant-service || true
            docker compose -f $COMPOSE_FILE logs --tail=100 marketdata-service || true
            docker compose -f $COMPOSE_FILE logs --tail=50 nginx || true

            # Check system resources
            echo "💾 System resources:"
            free -h
            df -h

            # Check for specific .NET issues
            echo "🔍 .NET Runtime debugging:"
            docker compose -f $COMPOSE_FILE logs --tail=20 marketdata-service | grep -i "exception\|error\|fail" || echo "No obvious errors in marketdata logs"
            docker compose -f $COMPOSE_FILE logs --tail=20 auth-service | grep -i "exception\|error\|fail" || echo "No obvious errors in auth logs"

            # Try to get more detailed error information
            echo "🔍 Detailed container inspection:"
            docker inspect abraapi-marketdata-ssl | grep -A 5 -B 5 "ExitCode" || true
            docker inspect abraapi-auth-ssl | grep -A 5 -B 5 "ExitCode" || true

            # Attempt recovery for failing services
            if [ "$RESTARTING_COUNT" -gt 0 ]; then
              echo "🔄 Attempting to recover failing services..."

              # Stop and remove failing containers
              docker compose -f $COMPOSE_FILE stop marketdata-service auth-service || true
              docker compose -f $COMPOSE_FILE rm -f marketdata-service auth-service || true

              # Clear any corrupted data
              docker system prune -f || true

              # Rebuild and restart failing services
              echo "🔨 Rebuilding failing services..."
              docker compose -f $COMPOSE_FILE build --no-cache marketdata-service auth-service

              # Start services one by one
              echo "🚀 Starting services individually..."
              docker compose -f $COMPOSE_FILE up -d redis
              sleep 10
              docker compose -f $COMPOSE_FILE up -d auth-service
              sleep 30
              docker compose -f $COMPOSE_FILE up -d marketdata-service
              sleep 30
              docker compose -f $COMPOSE_FILE up -d thread-service
              sleep 30

              # Check status after recovery
              echo "📊 Status after recovery attempt:"
              docker compose -f $COMPOSE_FILE ps
            fi

            # Special debugging for thread-service if it's running
            if docker compose -f $COMPOSE_FILE ps thread-service | grep -q "Up"; then
              echo "🔍 Thread Service debugging:"
              echo "Environment variables in container:"
              docker compose -f $COMPOSE_FILE exec -T thread-service env | grep -i supabase || echo "No Supabase env vars found"
              echo "Service status endpoint:"
              docker compose -f $COMPOSE_FILE exec -T thread-service curl -s http://localhost:8080/ || echo "Service not responding"
            fi
          else
            echo "✅ All services are healthy!"
          fi

          # Test SSL if enabled
          if [ "$COMPOSE_FILE" = "docker-compose.ssl.yml" ]; then
            echo "🔐 Testing SSL configuration..."
            sleep 10
            curl -I https://abraapp.undeclab.com/health --connect-timeout 10 || echo "⚠️ HTTPS health check failed"
            curl -I http://abraapp.undeclab.com/health --connect-timeout 10 || echo "⚠️ HTTP health check failed"
          else
            echo "🌐 Testing HTTP configuration..."
            curl -I http://abraapp.undeclab.com/health --connect-timeout 10 || echo "⚠️ HTTP health check failed"
          fi

          echo "✅ Deployment completed successfully!"

          # Show final status
          if [ "$COMPOSE_FILE" = "docker-compose.ssl.yml" ]; then
            echo "🌐 Your API is available at:"
            echo "  - HTTPS: https://abraapp.undeclab.com"
            echo "  - HTTP:  http://abraapp.undeclab.com (redirects to HTTPS)"
          else
            echo "🌐 Your API is available at: http://abraapp.undeclab.com"
          fi