name: SSL Certificate Renewal

on:
  schedule:
    # Run every day at 2 AM UTC to check for certificate renewal
    - cron: '0 2 * * *'
  workflow_dispatch:  # Allow manual triggers
    inputs:
      force_renewal:
        description: 'Force certificate renewal (even if not due)'
        required: false
        default: 'false'
        type: boolean

jobs:
  renew-ssl:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Renew SSL Certificates
      uses: appleboy/ssh-action@v1.2.2
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: 22
        timeout: 1800s  # 30 minutes
        command_timeout: 1800s
        script: |
          set -e
          
          echo "🔐 Starting SSL certificate renewal process..."
          cd ~/abraapi

          # Check if SSL is configured
          if [ ! -f "docker-compose.ssl.yml" ]; then
            echo "⚠️ SSL not configured, skipping renewal"
            exit 0
          fi

          # Check if certificates exist
          if [ ! -f "certbot/conf/live/abraapp.undeclab.com/fullchain.pem" ]; then
            echo "⚠️ No existing certificates found, skipping renewal"
            exit 0
          fi

          # Check certificate expiry (only renew if expires within 30 days)
          CERT_FILE="certbot/conf/live/abraapp.undeclab.com/fullchain.pem"
          EXPIRY_DATE=$(openssl x509 -enddate -noout -in "$CERT_FILE" | cut -d= -f2)
          EXPIRY_EPOCH=$(date -d "$EXPIRY_DATE" +%s)
          CURRENT_EPOCH=$(date +%s)
          DAYS_UNTIL_EXPIRY=$(( ($EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))

          echo "📅 Certificate expires in $DAYS_UNTIL_EXPIRY days"

          SHOULD_RENEW=false
          if [ "${{ github.event.inputs.force_renewal }}" = "true" ]; then
            echo "🔄 Forcing renewal as requested"
            SHOULD_RENEW=true
          elif [ $DAYS_UNTIL_EXPIRY -le 30 ]; then
            echo "⏰ Certificate expires within 30 days, renewing"
            SHOULD_RENEW=true
          else
            echo "✅ Certificate is still valid for $DAYS_UNTIL_EXPIRY days, no renewal needed"
          fi

          if [ "$SHOULD_RENEW" = "true" ]; then
            echo "🔄 Renewing SSL certificates..."
            
            # Make sure setup script is executable
            chmod +x setup-ssl.sh
            
            # Update email in setup script if provided
            if [ -n "${{ secrets.SSL_EMAIL }}" ]; then
              sed -i 's/EMAIL="<EMAIL>"/EMAIL="${{ secrets.SSL_EMAIL }}"/' setup-ssl.sh
            fi
            
            # Attempt renewal
            if ./setup-ssl.sh renew; then
              echo "✅ SSL certificate renewal successful"
              
              # Test the renewed certificate
              sleep 10
              if curl -I https://abraapp.undeclab.com/health --connect-timeout 10; then
                echo "✅ SSL certificate is working correctly"
              else
                echo "⚠️ SSL certificate test failed"
              fi
            else
              echo "❌ SSL certificate renewal failed"
              exit 1
            fi
          fi

          echo "✅ SSL renewal process completed"

    - name: Notify on Failure
      if: failure()
      uses: appleboy/ssh-action@v1.2.2
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: 22
        script: |
          echo "❌ SSL renewal failed at $(date)"
          echo "Check the GitHub Actions logs for details"
          # You can add notification logic here (email, Slack, etc.)
