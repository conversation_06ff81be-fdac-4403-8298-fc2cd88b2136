# .NET 10 Build artifacts
bin/
obj/
out/
publish/
artifacts/

# .NET 10 specific cache and temp directories
.dotnet/
.nuget/
packages/
TestResults/
BenchmarkDotNet.Artifacts/

# IDE files
.vs/
.vscode/
.idea/
*.user
*.suo
*.userosscache
*.sln.docstates
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# Git
.git/
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# Test files
*Tests/
*Test/
*.Tests/
*.Test/
test-results/
coverage/

# Logs and diagnostics
logs/
*.log
*.log.*
*.trace
*.etl

# Runtime files
*.pid
*.seed
*.pid.lock

# .NET 10 specific runtime files
*.runtimeconfig.json
*.deps.json

# Node.js (if any frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Docker files (except the one being used)
Dockerfile*
.dockerignore
docker-compose*.yml

# Environment files
.env*
appsettings.Development.json
appsettings.Local.json

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Package files
*.nupkg
*.snupkg

# JetBrains Rider
.idea/
*.sln.iml

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Backup files
*.bak
*.backup
*~