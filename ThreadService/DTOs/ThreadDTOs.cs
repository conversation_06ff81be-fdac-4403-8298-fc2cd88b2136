using System.ComponentModel.DataAnnotations;

namespace ThreadService.DTOs
{
    public class CreateThreadRequest
    {
        [Required]
        public string Content { get; set; } = string.Empty;
    }

    public class UpdateThreadRequest
    {
        [Required]
        public string Content { get; set; } = string.Empty;
    }

    public class ThreadResponse
    {
        public string Id { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int LikeCount { get; set; }
        public int CommentCount { get; set; }
        public int ShareCount { get; set; }
        public bool IsLikedByCurrentUser { get; set; }
        public bool IsSharedByCurrentUser { get; set; }
        public List<CommentResponse> Comments { get; set; } = new();
    }

    public class ThreadFeedResponse
    {
        public List<ThreadResponse> Threads { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage { get; set; }
    }

    public class ThreadFeedRequest
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? UserId { get; set; }
        public ThreadSortBy SortBy { get; set; } = ThreadSortBy.CreatedAt;
        public SortDirection SortDirection { get; set; } = SortDirection.Descending;
    }

    public enum ThreadSortBy
    {
        CreatedAt = 0,
        LikeCount = 1,
        CommentCount = 2,
        ShareCount = 3
    }
}
