using System.ComponentModel.DataAnnotations;

namespace ThreadService.DTOs
{
    public class LikeRequest
    {
        [Required]
        public string ThreadId { get; set; } = string.Empty;
    }

    public class LikeResponse
    {
        public bool IsLiked { get; set; }
        public DateTime? LikedAt { get; set; }
        public int TotalLikes { get; set; }
    }

    public class ShareRequest
    {
        [Required]
        public string ThreadId { get; set; } = string.Empty;
    }

    public class ShareResponse
    {
        public bool IsShared { get; set; }
        public DateTime? SharedAt { get; set; }
        public int TotalShares { get; set; }
    }

    public class LikeActivityResponse
    {
        public string Id { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string ThreadId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public ThreadResponse? Thread { get; set; }
    }

    public class ShareActivityResponse
    {
        public string Id { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string ThreadId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public ThreadResponse? Thread { get; set; }
    }
}
