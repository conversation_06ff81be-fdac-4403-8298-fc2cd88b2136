using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ThreadService.DTOs;
using ThreadService.Services;

namespace ThreadService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class CommentsController : ControllerBase
    {
        private readonly ICommentService _commentService;

        public CommentsController(ICommentService commentService)
        {
            _commentService = commentService;
        }

        private string GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                ?? User.FindFirst("sub")?.Value
                ?? throw new UnauthorizedAccessException("User ID not found in token");
        }

        private string? GetCurrentUserIdOrNull()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                ?? User.FindFirst("sub")?.Value;
        }

        [HttpPost]
        public async Task<ActionResult<CommentResponse>> CreateComment([FromBody] CreateCommentRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var comment = await _commentService.CreateCommentAsync(userId, request);
                return CreatedAtAction(nameof(GetComment), new { id = comment.Id }, comment);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        [AllowAnonymous]
        public async Task<ActionResult<CommentResponse>> GetComment(string id)
        {
            var currentUserId = GetCurrentUserIdOrNull();
            var comment = await _commentService.GetCommentByIdAsync(id, currentUserId);

            if (comment == null)
                return NotFound();

            return Ok(comment);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<CommentResponse>> UpdateComment(string id, [FromBody] UpdateCommentRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var comment = await _commentService.UpdateCommentAsync(userId, id, request);
                return Ok(comment);
            }
            catch (UnauthorizedAccessException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteComment(string id)
        {
            var userId = GetCurrentUserId();
            var success = await _commentService.DeleteCommentAsync(userId, id);

            if (!success)
                return NotFound();

            return NoContent();
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<CommentFeedResponse>> GetComments([FromQuery] CommentFeedRequest request)
        {
            var currentUserId = GetCurrentUserIdOrNull();
            var feed = await _commentService.GetCommentFeedAsync(request, currentUserId);
            return Ok(feed);
        }

        [HttpGet("thread/{threadId}")]
        [AllowAnonymous]
        public async Task<ActionResult<CommentFeedResponse>> GetThreadComments(string threadId, [FromQuery] CommentFeedRequest request)
        {
            var currentUserId = GetCurrentUserIdOrNull();
            var feed = await _commentService.GetThreadCommentsAsync(threadId, request, currentUserId);
            return Ok(feed);
        }
    }
}
