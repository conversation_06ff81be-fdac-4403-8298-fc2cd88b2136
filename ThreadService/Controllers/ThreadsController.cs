using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ThreadService.DTOs;
using ThreadService.Services;

namespace ThreadService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ThreadsController : ControllerBase
    {
        private readonly IThreadService _threadService;

        public ThreadsController(IThreadService threadService)
        {
            _threadService = threadService;
        }

        private string GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                ?? User.FindFirst("sub")?.Value
                ?? throw new UnauthorizedAccessException("User ID not found in token");
        }

        private string? GetCurrentUserIdOrNull()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                ?? User.FindFirst("sub")?.Value;
        }

        [HttpPost]
        public async Task<ActionResult<ThreadResponse>> CreateThread([FromBody] CreateThreadRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var thread = await _threadService.CreateThreadAsync(userId, request);
                return CreatedAtAction(nameof(GetThread), new { id = thread.Id }, thread);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("{id:guid}")]
        [AllowAnonymous]
        public async Task<ActionResult<ThreadResponse>> GetThread(string id)
        {
            var currentUserId = GetCurrentUserIdOrNull();
            var thread = await _threadService.GetThreadByIdAsync(id, currentUserId);

            if (thread == null)
                return NotFound();

            return Ok(thread);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<ThreadResponse>> UpdateThread(string id, [FromBody] UpdateThreadRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var thread = await _threadService.UpdateThreadAsync(userId, id, request);
                return Ok(thread);
            }
            catch (UnauthorizedAccessException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteThread(string id)
        {
            var userId = GetCurrentUserId();
            var success = await _threadService.DeleteThreadAsync(userId, id);

            if (!success)
                return NotFound();

            return NoContent();
        }

        [HttpGet("api-test")]
        [AllowAnonymous]
        public ActionResult<object> TestEndpoint()
        {
            return Ok(new
            {
                message = "ThreadService API is working!",
                timestamp = DateTime.UtcNow,
                endpoints = new
                {
                    threads = "/api/threads",
                    comments = "/api/comments",
                    likes = "/api/likes/toggle",
                    shares = "/api/likes/share/toggle"
                }
            });
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<ThreadFeedResponse>> GetThreads([FromQuery] ThreadFeedRequest request)
        {
            var currentUserId = GetCurrentUserIdOrNull();
            var feed = await _threadService.GetThreadFeedAsync(request, currentUserId);
            return Ok(feed);
        }

        [HttpGet("user/{userId}")]
        [AllowAnonymous]
        public async Task<ActionResult<ThreadFeedResponse>> GetUserThreads(string userId, [FromQuery] ThreadFeedRequest request)
        {
            var currentUserId = GetCurrentUserIdOrNull();
            var feed = await _threadService.GetUserThreadsAsync(userId, request, currentUserId);
            return Ok(feed);
        }


    }
}
