using Microsoft.EntityFrameworkCore;
using ThreadService.Data;
using ThreadService.DTOs;
using ThreadService.Models;

namespace ThreadService.Services
{
    public class LikeShareService : ILikeShareService
    {
        private readonly ThreadContext _context;

        public LikeShareService(ThreadContext context)
        {
            _context = context;
        }

        public async Task<LikeResponse> ToggleLikeAsync(string userId, string threadId)
        {
            var threadGuid = Guid.Parse(threadId);
            var userGuid = Guid.Parse(userId);

            // Check if thread exists
            var threadExists = await _context.Threads.AnyAsync(t => t.Id == threadGuid);
            if (!threadExists)
                throw new ArgumentException("Thread not found");

            // Check if user already liked
            var existingLike = await _context.Likes
                .FirstOrDefaultAsync(l => l.UserId == userGuid && l.ThreadId == threadGuid);

            if (existingLike != null)
            {
                // Remove like (toggle off)
                _context.Likes.Remove(existingLike);
                await _context.SaveChangesAsync();

                return await GetLikeStatusAsync(threadId, userId);
            }
            else
            {
                // Add new like
                var like = new Like
                {
                    UserId = userGuid,
                    ThreadId = threadGuid
                };

                _context.Likes.Add(like);
                await _context.SaveChangesAsync();

                return await GetLikeStatusAsync(threadId, userId);
            }
        }

        public async Task<ShareResponse> ToggleShareAsync(string userId, string threadId)
        {
            var threadGuid = Guid.Parse(threadId);
            var userGuid = Guid.Parse(userId);

            // Check if thread exists
            var threadExists = await _context.Threads.AnyAsync(t => t.Id == threadGuid);
            if (!threadExists)
                throw new ArgumentException("Thread not found");

            // Check if user already shared
            var existingShare = await _context.Shares
                .FirstOrDefaultAsync(s => s.UserId == userGuid && s.ThreadId == threadGuid);

            if (existingShare != null)
            {
                // Remove share (toggle off)
                _context.Shares.Remove(existingShare);
                await _context.SaveChangesAsync();

                return await GetShareStatusAsync(threadId, userId);
            }
            else
            {
                // Add new share
                var share = new Share
                {
                    UserId = userGuid,
                    ThreadId = threadGuid
                };

                _context.Shares.Add(share);
                await _context.SaveChangesAsync();

                return await GetShareStatusAsync(threadId, userId);
            }
        }

        private async Task<LikeResponse> GetLikeStatusAsync(string threadId, string userId)
        {
            var threadGuid = Guid.Parse(threadId);
            var userGuid = Guid.Parse(userId);

            var userLike = await _context.Likes
                .FirstOrDefaultAsync(l => l.UserId == userGuid && l.ThreadId == threadGuid);

            var totalLikes = await _context.Likes
                .CountAsync(l => l.ThreadId == threadGuid);

            return new LikeResponse
            {
                IsLiked = userLike != null,
                LikedAt = userLike?.CreatedAt,
                TotalLikes = totalLikes
            };
        }

        private async Task<ShareResponse> GetShareStatusAsync(string threadId, string userId)
        {
            var threadGuid = Guid.Parse(threadId);
            var userGuid = Guid.Parse(userId);

            var userShare = await _context.Shares
                .FirstOrDefaultAsync(s => s.UserId == userGuid && s.ThreadId == threadGuid);

            var totalShares = await _context.Shares
                .CountAsync(s => s.ThreadId == threadGuid);

            return new ShareResponse
            {
                IsShared = userShare != null,
                SharedAt = userShare?.CreatedAt,
                TotalShares = totalShares
            };
        }
    }
}
