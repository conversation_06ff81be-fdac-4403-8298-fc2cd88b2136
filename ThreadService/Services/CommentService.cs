using Microsoft.EntityFrameworkCore;
using ThreadService.Data;
using ThreadService.DTOs;
using ThreadService.Models;

namespace ThreadService.Services
{
    public class CommentService : ICommentService
    {
        private readonly ThreadContext _context;

        public CommentService(ThreadContext context)
        {
            _context = context;
        }

        public async Task<CommentResponse> CreateCommentAsync(string userId, CreateCommentRequest request)
        {
            var comment = new Comment
            {
                ThreadId = Guid.Parse(request.ThreadId),
                UserId = Guid.Parse(userId),
                Content = request.Content
            };

            _context.Comments.Add(comment);
            await _context.SaveChangesAsync();

            return await GetCommentByIdAsync(comment.Id.ToString(), userId) ?? throw new InvalidOperationException("Failed to create comment");
        }

        public async Task<CommentResponse> UpdateCommentAsync(string userId, string commentId, UpdateCommentRequest request)
        {
            var commentGuid = Guid.Parse(commentId);
            var userGuid = Guid.Parse(userId);
            var comment = await _context.Comments.FirstOrDefaultAsync(c => c.Id == commentGuid && c.UserId == userGuid);
            if (comment == null)
                throw new UnauthorizedAccessException("Comment not found or access denied");

            comment.Content = request.Content;
            comment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return await GetCommentByIdAsync(commentId, userId) ?? throw new InvalidOperationException("Failed to update comment");
        }

        public async Task<bool> DeleteCommentAsync(string userId, string commentId)
        {
            var commentGuid = Guid.Parse(commentId);
            var userGuid = Guid.Parse(userId);
            var comment = await _context.Comments.FirstOrDefaultAsync(c => c.Id == commentGuid && c.UserId == userGuid);
            if (comment == null)
                return false;

            _context.Comments.Remove(comment);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<CommentResponse?> GetCommentByIdAsync(string commentId, string? currentUserId = null)
        {
            var commentGuid = Guid.Parse(commentId);
            var comment = await _context.Comments
                .FirstOrDefaultAsync(c => c.Id == commentGuid);

            if (comment == null)
                return null;

            return new CommentResponse
            {
                Id = comment.Id.ToString(),
                ThreadId = comment.ThreadId.ToString(),
                UserId = comment.UserId.ToString(),
                Content = comment.Content,
                CreatedAt = comment.CreatedAt,
                UpdatedAt = comment.UpdatedAt
            };
        }

        public async Task<CommentFeedResponse> GetCommentFeedAsync(CommentFeedRequest request, string? currentUserId = null)
        {
            var query = _context.Comments.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(request.ThreadId))
            {
                var threadGuid = Guid.Parse(request.ThreadId);
                query = query.Where(c => c.ThreadId == threadGuid);
            }

            if (!string.IsNullOrEmpty(request.UserId))
            {
                var userGuid = Guid.Parse(request.UserId);
                query = query.Where(c => c.UserId == userGuid);
            }

            // Apply sorting
            query = request.SortBy switch
            {
                CommentSortBy.UpdatedAt => request.SortDirection == SortDirection.Descending
                    ? query.OrderByDescending(c => c.UpdatedAt ?? c.CreatedAt)
                    : query.OrderBy(c => c.UpdatedAt ?? c.CreatedAt),
                _ => request.SortDirection == SortDirection.Descending
                    ? query.OrderByDescending(c => c.CreatedAt)
                    : query.OrderBy(c => c.CreatedAt)
            };

            var totalCount = await query.CountAsync();
            var comments = await query
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            var commentResponses = comments.Select(comment => new CommentResponse
            {
                Id = comment.Id.ToString(),
                ThreadId = comment.ThreadId.ToString(),
                UserId = comment.UserId.ToString(),
                Content = comment.Content,
                CreatedAt = comment.CreatedAt,
                UpdatedAt = comment.UpdatedAt
            }).ToList();

            return new CommentFeedResponse
            {
                Comments = commentResponses,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                HasNextPage = (request.Page * request.PageSize) < totalCount
            };
        }

        public async Task<CommentFeedResponse> GetThreadCommentsAsync(string threadId, CommentFeedRequest request, string? currentUserId = null)
        {
            request.ThreadId = threadId;
            return await GetCommentFeedAsync(request, currentUserId);
        }
    }
}
