using ThreadService.DTOs;

namespace ThreadService.Services
{
    public interface ICommentService
    {
        Task<CommentResponse> CreateCommentAsync(string userId, CreateCommentRequest request);
        Task<CommentResponse> UpdateCommentAsync(string userId, string commentId, UpdateCommentRequest request);
        Task<bool> DeleteCommentAsync(string userId, string commentId);
        Task<CommentResponse?> GetCommentByIdAsync(string commentId, string? currentUserId = null);
        Task<CommentFeedResponse> GetCommentFeedAsync(CommentFeedRequest request, string? currentUserId = null);
        Task<CommentFeedResponse> GetThreadCommentsAsync(string threadId, CommentFeedRequest request, string? currentUserId = null);
    }
}
