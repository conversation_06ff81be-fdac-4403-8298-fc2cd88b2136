using Microsoft.EntityFrameworkCore;
using ThreadService.Data;
using ThreadService.DTOs;
using ThreadService.Models;

namespace ThreadService.Services
{
    public class ThreadService : IThreadService
    {
        private readonly ThreadContext _context;

        public ThreadService(ThreadContext context)
        {
            _context = context;
        }

        public async Task<ThreadResponse> CreateThreadAsync(string userId, CreateThreadRequest request)
        {
            var thread = new Models.Thread
            {
                UserId = Guid.Parse(userId),
                Content = request.Content
            };

            _context.Threads.Add(thread);
            await _context.SaveChangesAsync();

            return await GetThreadByIdAsync(thread.Id.ToString(), userId) ?? throw new InvalidOperationException("Failed to create thread");
        }

        public async Task<ThreadResponse> UpdateThreadAsync(string userId, string threadId, UpdateThreadRequest request)
        {
            var threadGuid = Guid.Parse(threadId);
            var userGuid = Guid.Parse(userId);
            var thread = await _context.Threads.FirstOrDefaultAsync(t => t.Id == threadGuid && t.UserId == userGuid);
            if (thread == null)
                throw new UnauthorizedAccessException("Thread not found or access denied");

            thread.Content = request.Content;
            thread.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return await GetThreadByIdAsync(threadId, userId) ?? throw new InvalidOperationException("Failed to update thread");
        }

        public async Task<bool> DeleteThreadAsync(string userId, string threadId)
        {
            var threadGuid = Guid.Parse(threadId);
            var userGuid = Guid.Parse(userId);
            var thread = await _context.Threads.FirstOrDefaultAsync(t => t.Id == threadGuid && t.UserId == userGuid);
            if (thread == null)
                return false;

            _context.Threads.Remove(thread);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<ThreadResponse?> GetThreadByIdAsync(string threadId, string? currentUserId = null)
        {
            var threadGuid = Guid.Parse(threadId);
            var thread = await _context.Threads
                .Include(t => t.Likes)
                .Include(t => t.Shares)
                .Include(t => t.Comments)
                .FirstOrDefaultAsync(t => t.Id == threadGuid);

            if (thread == null)
                return null;

            var currentUserGuid = currentUserId != null ? Guid.Parse(currentUserId) : (Guid?)null;
            var isLiked = currentUserGuid != null && thread.Likes.Any(l => l.UserId == currentUserGuid);
            var isShared = currentUserGuid != null && thread.Shares.Any(s => s.UserId == currentUserGuid);

            return new ThreadResponse
            {
                Id = thread.Id.ToString(),
                UserId = thread.UserId.ToString(),
                Content = thread.Content,
                CreatedAt = thread.CreatedAt,
                UpdatedAt = thread.UpdatedAt,
                LikeCount = thread.Likes.Count,
                CommentCount = thread.Comments.Count,
                ShareCount = thread.Shares.Count,
                IsLikedByCurrentUser = isLiked,
                IsSharedByCurrentUser = isShared,
                Comments = new List<CommentResponse>() // Can be populated separately if needed
            };
        }

        public async Task<ThreadFeedResponse> GetThreadFeedAsync(ThreadFeedRequest request, string? currentUserId = null)
        {
            var query = _context.Threads.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(request.UserId))
            {
                var userGuid = Guid.Parse(request.UserId);
                query = query.Where(t => t.UserId == userGuid);
            }

            // Apply sorting
            query = request.SortBy switch
            {
                ThreadSortBy.LikeCount => request.SortDirection == SortDirection.Descending
                    ? query.OrderByDescending(t => t.Likes.Count)
                    : query.OrderBy(t => t.Likes.Count),
                ThreadSortBy.CommentCount => request.SortDirection == SortDirection.Descending
                    ? query.OrderByDescending(t => t.Comments.Count)
                    : query.OrderBy(t => t.Comments.Count),
                ThreadSortBy.ShareCount => request.SortDirection == SortDirection.Descending
                    ? query.OrderByDescending(t => t.Shares.Count)
                    : query.OrderBy(t => t.Shares.Count),
                _ => request.SortDirection == SortDirection.Descending
                    ? query.OrderByDescending(t => t.CreatedAt)
                    : query.OrderBy(t => t.CreatedAt)
            };

            var totalCount = await query.CountAsync();
            var threads = await query
                .Include(t => t.Likes)
                .Include(t => t.Shares)
                .Include(t => t.Comments)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            var currentUserGuid = currentUserId != null ? Guid.Parse(currentUserId) : (Guid?)null;
            var threadResponses = new List<ThreadResponse>();
            foreach (var thread in threads)
            {
                var isLiked = currentUserGuid != null && thread.Likes.Any(l => l.UserId == currentUserGuid);
                var isShared = currentUserGuid != null && thread.Shares.Any(s => s.UserId == currentUserGuid);

                threadResponses.Add(new ThreadResponse
                {
                    Id = thread.Id.ToString(),
                    UserId = thread.UserId.ToString(),
                    Content = thread.Content,
                    CreatedAt = thread.CreatedAt,
                    UpdatedAt = thread.UpdatedAt,
                    LikeCount = thread.Likes.Count,
                    CommentCount = thread.Comments.Count,
                    ShareCount = thread.Shares.Count,
                    IsLikedByCurrentUser = isLiked,
                    IsSharedByCurrentUser = isShared,
                    Comments = new List<CommentResponse>() // Can be populated separately if needed
                });
            }

            return new ThreadFeedResponse
            {
                Threads = threadResponses,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                HasNextPage = (request.Page * request.PageSize) < totalCount
            };
        }

        public async Task<ThreadFeedResponse> GetUserThreadsAsync(string userId, ThreadFeedRequest request, string? currentUserId = null)
        {
            request.UserId = userId;
            return await GetThreadFeedAsync(request, currentUserId);
        }


    }
}
