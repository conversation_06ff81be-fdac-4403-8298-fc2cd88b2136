using ThreadService.DTOs;

namespace ThreadService.Services
{
    public interface IThreadService
    {
        Task<ThreadResponse> CreateThreadAsync(string userId, CreateThreadRequest request);
        Task<ThreadResponse> UpdateThreadAsync(string userId, string threadId, UpdateThreadRequest request);
        Task<bool> DeleteThreadAsync(string userId, string threadId);
        Task<ThreadResponse?> GetThreadByIdAsync(string threadId, string? currentUserId = null);
        Task<ThreadFeedResponse> GetThreadFeedAsync(ThreadFeedRequest request, string? currentUserId = null);
        Task<ThreadFeedResponse> GetUserThreadsAsync(string userId, ThreadFeedRequest request, string? currentUserId = null);
    }
}
