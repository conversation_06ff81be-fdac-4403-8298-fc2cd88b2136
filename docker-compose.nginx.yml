version: '3.8'

services:
  # Nginx proxy for blue-green deployment
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.blue-green.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/upstreams.conf:/etc/nginx/upstreams.conf:ro
      - ./certbot/www:/var/www/certbot:ro
      - ./certbot/conf:/etc/letsencrypt:ro
      - nginx_logs:/var/log/nginx
    networks:
      - abraapi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Certbot for SSL certificates
  certbot:
    image: certbot/certbot:latest
    container_name: abraapi-certbot
    volumes:
      - ./certbot/www:/var/www/certbot:rw
      - ./certbot/conf:/etc/letsencrypt:rw
    command: certonly --webroot --webroot-path=/var/www/certbot --email ${SSL_EMAIL:-<EMAIL>} --agree-tos --no-eff-email -d abraapp.undeclab.com
    depends_on:
      - nginx
    networks:
      - abraapi-network

networks:
  abraapi-network:
    driver: bridge

volumes:
  nginx_logs:
