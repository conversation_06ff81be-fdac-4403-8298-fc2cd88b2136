#!/bin/bash

# Deployment State Management Utility for ABRAAPI
# Manages deployment state tracking for intelligent deployments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Configuration
DEPLOYMENT_STATE_FILE=".deployment_state"
DEPLOYMENT_HISTORY_FILE=".deployment_history"
MAX_HISTORY_ENTRIES=50

# Function to get current deployment state
get_current_state() {
    if [ -f "$DEPLOYMENT_STATE_FILE" ]; then
        cat "$DEPLOYMENT_STATE_FILE"
    else
        echo ""
    fi
}

# Function to save deployment state
save_state() {
    local commit_hash=${1:-$(git rev-parse HEAD)}
    local message=${2:-"Deployment"}
    local timestamp=$(date -Iseconds)

    # Save current state
    echo "$commit_hash" > "$DEPLOYMENT_STATE_FILE"

    # Add to history
    echo "$timestamp|$commit_hash|$message" >> "$DEPLOYMENT_HISTORY_FILE"

    # Trim history to max entries
    if [ -f "$DEPLOYMENT_HISTORY_FILE" ]; then
        tail -n "$MAX_HISTORY_ENTRIES" "$DEPLOYMENT_HISTORY_FILE" > "${DEPLOYMENT_HISTORY_FILE}.tmp"
        mv "${DEPLOYMENT_HISTORY_FILE}.tmp" "$DEPLOYMENT_HISTORY_FILE"
    fi

    print_success "Deployment state saved: $(echo $commit_hash | cut -c1-8)"
}

# Function to show deployment history
show_history() {
    local limit=${1:-10}
    
    if [ ! -f "$DEPLOYMENT_HISTORY_FILE" ]; then
        print_warning "No deployment history found"
        return
    fi
    
    print_header "📋 Deployment History (last $limit entries):"
    echo ""
    
    tail -n "$limit" "$DEPLOYMENT_HISTORY_FILE" | while IFS='|' read -r timestamp commit message; do
        local short_commit=$(echo "$commit" | cut -c1-8)
        local formatted_time=$(date -d "$timestamp" '+%Y-%m-%d %H:%M:%S' 2>/dev/null || echo "$timestamp")
        local commit_info=""
        
        if git cat-file -e "$commit" 2>/dev/null; then
            commit_info=$(git log --format='%s' -n 1 "$commit" 2>/dev/null || echo "")
        fi
        
        echo "  $formatted_time - $short_commit - $message"
        if [ -n "$commit_info" ]; then
            echo "    └─ $commit_info"
        fi
    done
}

# Function to show current status
show_status() {
    local current_state=$(get_current_state)
    local current_commit=$(git rev-parse HEAD)
    local current_short=$(git rev-parse --short HEAD)
    
    print_header "📊 Deployment Status"
    echo ""
    
    if [ -z "$current_state" ]; then
        print_warning "No deployment state found"
        echo "  This appears to be the first deployment"
    else
        local state_short=$(echo "$current_state" | cut -c1-8)
        echo "  Last deployed commit: $state_short"
        
        if git cat-file -e "$current_state" 2>/dev/null; then
            local state_info=$(git log --format='%h - %s (%cr)' -n 1 "$current_state" 2>/dev/null)
            echo "    └─ $state_info"
        else
            print_warning "Last deployed commit not found in current repository"
        fi
    fi
    
    echo "  Current commit: $current_short"
    local current_info=$(git log --format='%h - %s (%cr)' -n 1 "$current_commit" 2>/dev/null)
    echo "    └─ $current_info"
    
    echo ""
    
    if [ "$current_state" = "$current_commit" ]; then
        print_success "Repository is up to date with last deployment"
    elif [ -z "$current_state" ]; then
        print_status "Ready for first deployment"
    else
        print_status "Repository has changes since last deployment"
        
        if git cat-file -e "$current_state" 2>/dev/null; then
            local commit_count=$(git rev-list --count "$current_state..$current_commit" 2>/dev/null || echo "unknown")
            echo "  Commits ahead: $commit_count"
        fi
    fi
}

# Function to reset deployment state
reset_state() {
    local confirm=${1:-false}
    
    if [ "$confirm" != "true" ] && [ "$confirm" != "--force" ]; then
        print_warning "This will reset the deployment state. Are you sure? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            print_status "Reset cancelled"
            return
        fi
    fi
    
    if [ -f "$DEPLOYMENT_STATE_FILE" ]; then
        rm "$DEPLOYMENT_STATE_FILE"
        print_success "Deployment state reset"
    else
        print_status "No deployment state to reset"
    fi
}

# Function to rollback to previous state
rollback() {
    local steps=${1:-1}
    
    if [ ! -f "$DEPLOYMENT_HISTORY_FILE" ]; then
        print_error "No deployment history found"
        return 1
    fi
    
    local total_entries=$(wc -l < "$DEPLOYMENT_HISTORY_FILE")
    if [ "$steps" -ge "$total_entries" ]; then
        print_error "Cannot rollback $steps steps, only $total_entries entries in history"
        return 1
    fi
    
    local target_line=$((total_entries - steps))
    local target_entry=$(sed -n "${target_line}p" "$DEPLOYMENT_HISTORY_FILE")
    
    if [ -z "$target_entry" ]; then
        print_error "Could not find target deployment entry"
        return 1
    fi
    
    local target_commit=$(echo "$target_entry" | cut -d'|' -f2)
    local target_timestamp=$(echo "$target_entry" | cut -d'|' -f1)
    
    print_warning "Rolling back to commit: $(echo "$target_commit" | cut -c1-8)"
    print_warning "Deployment time: $target_timestamp"
    
    if git cat-file -e "$target_commit" 2>/dev/null; then
        local commit_info=$(git log --format='%s' -n 1 "$target_commit" 2>/dev/null)
        print_status "Commit message: $commit_info"
    fi
    
    echo ""
    print_warning "Are you sure you want to rollback? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_status "Rollback cancelled"
        return
    fi
    
    echo "$target_commit" > "$DEPLOYMENT_STATE_FILE"
    print_success "Deployment state rolled back to: $(echo "$target_commit" | cut -c1-8)"
    print_status "Note: This only changes the deployment state. You may need to redeploy."
}

# Function to show help
show_help() {
    echo "Usage: $0 COMMAND [OPTIONS]"
    echo ""
    echo "Manage deployment state for intelligent deployments."
    echo ""
    echo "Commands:"
    echo "  status                Show current deployment status"
    echo "  save [COMMIT] [MSG]   Save deployment state (default: current commit)"
    echo "  history [LIMIT]       Show deployment history (default: 10 entries)"
    echo "  reset [--force]       Reset deployment state"
    echo "  rollback [STEPS]      Rollback to previous deployment (default: 1 step)"
    echo "  help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 status             # Show current status"
    echo "  $0 save               # Save current commit as deployed"
    echo "  $0 save abc123 'Hot fix deployment'"
    echo "  $0 history 20         # Show last 20 deployments"
    echo "  $0 rollback 2         # Rollback 2 deployments"
    echo ""
}

# Main execution
case "${1:-status}" in
    "status"|"show")
        show_status
        ;;
    "save"|"mark")
        save_state "$2" "$3"
        ;;
    "history"|"log")
        show_history "$2"
        ;;
    "reset"|"clear")
        reset_state "$2"
        ;;
    "rollback"|"revert")
        rollback "$2"
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
