#!/bin/bash

# Aggressive Cleanup Script for ABRAAPI
# Use this when you need to completely clean up Docker resources

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Aggressive cleanup script for Docker resources."
    echo ""
    echo "Options:"
    echo "  --all, -a         Clean everything (containers, images, volumes, networks)"
    echo "  --containers, -c  Clean only containers"
    echo "  --images, -i      Clean only images"
    echo "  --volumes, -v     Clean only volumes"
    echo "  --networks, -n    Clean only networks"
    echo "  --cache          Clean only build cache"
    echo "  --force, -f      Skip confirmation prompts"
    echo "  --help, -h       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --all         # Clean everything (with confirmation)"
    echo "  $0 --all --force # Clean everything (no confirmation)"
    echo "  $0 --images      # Clean only images"
    echo ""
    echo "⚠️  WARNING: This script will remove Docker resources!"
    echo "   Use with caution in production environments."
}

# Function to confirm action
confirm_action() {
    local message="$1"
    local force="$2"
    
    if [ "$force" = "true" ]; then
        return 0
    fi
    
    print_warning "$message"
    echo -n "Are you sure? (y/N): "
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled"
        return 1
    fi
    return 0
}

# Function to clean containers
clean_containers() {
    local force="$1"
    
    if ! confirm_action "This will stop and remove all ABRAAPI containers" "$force"; then
        return
    fi
    
    print_header "🧹 Cleaning containers..."
    
    # Stop all services
    print_status "Stopping all services..."
    docker-compose down --remove-orphans || true
    
    # Remove all containers
    print_status "Removing all containers..."
    docker container prune -f || true
    
    print_success "Containers cleaned"
}

# Function to clean images
clean_images() {
    local force="$1"
    
    if ! confirm_action "This will remove all unused Docker images" "$force"; then
        return
    fi
    
    print_header "🧹 Cleaning images..."
    
    # Remove dangling images
    print_status "Removing dangling images..."
    docker image prune -f || true
    
    # Remove all unused images
    print_status "Removing all unused images..."
    docker image prune -a -f || true
    
    print_success "Images cleaned"
}

# Function to clean volumes
clean_volumes() {
    local force="$1"
    
    if ! confirm_action "This will remove all unused Docker volumes (DATA LOSS POSSIBLE!)" "$force"; then
        return
    fi
    
    print_header "🧹 Cleaning volumes..."
    
    # Remove unused volumes
    print_status "Removing unused volumes..."
    docker volume prune -f || true
    
    print_success "Volumes cleaned"
}

# Function to clean networks
clean_networks() {
    local force="$1"
    
    if ! confirm_action "This will remove all unused Docker networks" "$force"; then
        return
    fi
    
    print_header "🧹 Cleaning networks..."
    
    # Remove unused networks
    print_status "Removing unused networks..."
    docker network prune -f || true
    
    print_success "Networks cleaned"
}

# Function to clean build cache
clean_cache() {
    local force="$1"
    
    if ! confirm_action "This will remove all Docker build cache" "$force"; then
        return
    fi
    
    print_header "🧹 Cleaning build cache..."
    
    # Remove build cache
    print_status "Removing build cache..."
    docker builder prune -f || true
    
    # Clear NuGet cache
    print_status "Clearing NuGet cache..."
    docker run --rm -v $(pwd):/src -w /src mcr.microsoft.com/dotnet/sdk:10.0-preview \
        dotnet nuget locals all --clear || true
    
    print_success "Build cache cleaned"
}

# Function to clean everything
clean_all() {
    local force="$1"
    
    if ! confirm_action "This will remove ALL Docker resources (containers, images, volumes, networks, cache)" "$force"; then
        return
    fi
    
    print_header "🧹 Performing complete cleanup..."
    
    clean_containers "true"
    clean_images "true"
    clean_volumes "true"
    clean_networks "true"
    clean_cache "true"
    
    print_success "Complete cleanup finished"
}

# Function to show system info
show_system_info() {
    print_header "📊 Docker System Information"
    echo ""
    
    print_status "Docker system usage:"
    docker system df || true
    
    echo ""
    print_status "Running containers:"
    docker ps || true
    
    echo ""
    print_status "All images:"
    docker images || true
}

# Parse command line arguments
FORCE=false
ACTION=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            exit 0
            ;;
        --all|-a)
            ACTION="all"
            shift
            ;;
        --containers|-c)
            ACTION="containers"
            shift
            ;;
        --images|-i)
            ACTION="images"
            shift
            ;;
        --volumes|-v)
            ACTION="volumes"
            shift
            ;;
        --networks|-n)
            ACTION="networks"
            shift
            ;;
        --cache)
            ACTION="cache"
            shift
            ;;
        --force|-f)
            FORCE=true
            shift
            ;;
        --info)
            ACTION="info"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Default action if none specified
if [ -z "$ACTION" ]; then
    show_help
    exit 0
fi

# Execute the requested action
case "$ACTION" in
    "all")
        clean_all "$FORCE"
        ;;
    "containers")
        clean_containers "$FORCE"
        ;;
    "images")
        clean_images "$FORCE"
        ;;
    "volumes")
        clean_volumes "$FORCE"
        ;;
    "networks")
        clean_networks "$FORCE"
        ;;
    "cache")
        clean_cache "$FORCE"
        ;;
    "info")
        show_system_info
        ;;
    *)
        print_error "Invalid action: $ACTION"
        show_help
        exit 1
        ;;
esac

# Show final system info
if [ "$ACTION" != "info" ]; then
    echo ""
    show_system_info
fi
