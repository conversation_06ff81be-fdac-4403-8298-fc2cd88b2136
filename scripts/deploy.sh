#!/bin/bash

# Intelligent deployment script for ABRAAPI - deploys only modified services
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Configuration
DEPLOYMENT_STATE_FILE=".deployment_state"
FORCE_DEPLOY=${1:-false}

print_header "🚀 Starting ABRAAPI Intelligent Deployment..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    echo "Please create .env file from .env.example and configure your environment variables."
    exit 1
fi

# Load environment variables safely
if ! set -a && source .env && set +a; then
    print_error "Failed to load .env file. Please check for syntax errors."
    print_status "Common issues: unquoted values with special characters"
    exit 1
fi

# Validate required environment variables
required_vars=(
    "SUPABASE_CONNECTION_STRING"
    "SUPABASE_URL"
    "SUPABASE_JWT_SECRET"
    "POSTGRES_USER"
    "POSTGRES_PASSWORD"
    "POSTGRES_DB"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        print_error "Required environment variable $var is not set!"
        exit 1
    fi
done

print_success "Environment variables validated"

# Service definitions
declare -A SERVICES=(
    ["assistant-service"]="AssistantService"
    ["auth-service"]="AuthService"
    ["marketdata-service"]="MarketDataService"
    ["thread-service"]="ThreadService"
)

# Function to get last deployment commit
get_last_deployment_commit() {
    if [ -f "$DEPLOYMENT_STATE_FILE" ]; then
        cat "$DEPLOYMENT_STATE_FILE"
    else
        echo ""
    fi
}

# Function to save current deployment state
save_deployment_state() {
    git rev-parse HEAD > "$DEPLOYMENT_STATE_FILE"
    print_success "Deployment state saved: $(cat $DEPLOYMENT_STATE_FILE)"
}

# Function to detect changed services
detect_changed_services() {
    local last_commit=$(get_last_deployment_commit)
    local changed_services=()

    if [ -z "$last_commit" ] || [ "$FORCE_DEPLOY" = "true" ] || [ "$FORCE_DEPLOY" = "--force" ]; then
        print_warning "No previous deployment state found or force deploy requested"
        print_status "Will deploy all services"
        for service in "${!SERVICES[@]}"; do
            changed_services+=("$service")
        done
    else
        print_status "Checking for changes since commit: $last_commit"

        # Check if the commit exists
        if ! git cat-file -e "$last_commit" 2>/dev/null; then
            print_warning "Last deployment commit not found in current repository"
            print_status "Will deploy all services"
            for service in "${!SERVICES[@]}"; do
                changed_services+=("$service")
            done
        else
            # Check for changes in each service directory
            for service in "${!SERVICES[@]}"; do
                local service_dir="${SERVICES[$service]}"
                if git diff --quiet "$last_commit" HEAD -- "$service_dir/"; then
                    print_status "No changes detected in $service ($service_dir)"
                else
                    print_status "Changes detected in $service ($service_dir)"
                    changed_services+=("$service")
                fi
            done

            # Check for changes in docker-compose.yml, nginx config, or deployment scripts
            if git diff --quiet "$last_commit" HEAD -- "docker-compose.yml" "nginx/" "scripts/"; then
                print_status "No changes in infrastructure files"
            else
                print_warning "Infrastructure changes detected - will deploy all services"
                changed_services=()
                for service in "${!SERVICES[@]}"; do
                    changed_services+=("$service")
                done
            fi
        fi
    fi

    echo "${changed_services[@]}"
}

# Function to build specific services
build_services() {
    local services_to_build=($@)

    if [ ${#services_to_build[@]} -eq 0 ]; then
        print_success "No services need to be built"
        return 0
    fi

    print_header "🔨 Building modified services..."

    # Enable Docker BuildKit for better performance
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1

    # Build only the specified services
    for service in "${services_to_build[@]}"; do
        print_status "Building $service..."
        if docker-compose build "$service"; then
            print_success "$service built successfully"
        else
            print_error "Failed to build $service"
            exit 1
        fi
    done
}

# Function to deploy services
deploy_services() {
    local services_to_deploy=($@)

    print_header "🚀 Deploying services..."

    # Stop and remove containers only for services being redeployed
    if [ ${#services_to_deploy[@]} -gt 0 ]; then
        print_status "Stopping only the services that will be redeployed..."
        for service in "${services_to_deploy[@]}"; do
            print_status "Stopping $service..."
            docker-compose stop "$service" || true
            docker-compose rm -f "$service" || true
        done

        # Clean up only dangling images to free space
        print_status "Cleaning up dangling images..."
        docker image prune -f || true
    else
        print_status "No services to redeploy - all services will remain running"
    fi

    # Start all services (this will start new versions of rebuilt services and ensure others are running)
    print_status "Starting services..."
    docker-compose up -d
}

# Main deployment logic
main() {
    # Detect changed services
    changed_services=($(detect_changed_services))

    if [ ${#changed_services[@]} -eq 0 ]; then
        print_success "No services have changed since last deployment"
        print_status "Checking if all services are running..."
        docker-compose up -d
    else
        print_header "📋 Services to be deployed:"
        for service in "${changed_services[@]}"; do
            echo "  - $service (${SERVICES[$service]})"
        done
        echo ""

        # Build changed services
        build_services "${changed_services[@]}"

        # Deploy services
        deploy_services "${changed_services[@]}"
    fi

    # Wait for services to be healthy
    wait_for_healthy_services

    # Save deployment state
    save_deployment_state

    # Show final status
    show_deployment_summary
}

# Function to wait for services to be healthy
wait_for_healthy_services() {
    print_header "⏳ Waiting for services to be healthy..."
    local timeout=300  # 5 minutes
    local elapsed=0
    local interval=10

    while [ $elapsed -lt $timeout ]; do
        local unhealthy_count=$(docker-compose ps | grep -c "unhealthy\|starting" || echo "0")

        if [ "$unhealthy_count" -gt 0 ]; then
            print_status "Services still starting... (${elapsed}s/${timeout}s) - $unhealthy_count services not ready"
            sleep $interval
            elapsed=$((elapsed + interval))
        else
            print_success "All services are healthy!"
            return 0
        fi
    done

    print_error "Timeout waiting for services to be healthy"
    print_status "Showing logs for troubleshooting..."
    docker-compose logs --tail=50
    exit 1
}

# Function to show deployment summary
show_deployment_summary() {
    print_header "📊 Deployment Summary"

    # Show service status
    echo "Service Status:"
    docker-compose ps
    echo ""

    print_success "🎉 Deployment completed successfully!"
    echo ""
    echo "Services are available at:"
    echo "- Auth Service: http://localhost/auth"
    echo "- Thread Service: http://localhost/threads"
    echo "- Market Data Service: http://localhost/market"
    echo "- Assistant Service: http://localhost/assistant"
    echo ""
    echo "Health checks:"
    echo "- Overall: http://localhost/health"
    echo "- Individual services: http://localhost/{service}/health"
    echo ""

    # Show what was deployed
    local current_commit=$(git rev-parse --short HEAD)
    print_status "Deployed commit: $current_commit"
    print_status "Deployment timestamp: $(date)"
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Intelligent deployment script that only deploys modified services."
    echo ""
    echo "Options:"
    echo "  --force, -f    Force deployment of all services regardless of changes"
    echo "  --help, -h     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0             # Deploy only modified services"
    echo "  $0 --force     # Force deploy all services"
    echo ""
    echo "The script uses Git to detect changes since the last deployment."
    echo "On first run or when no deployment state exists, all services are deployed."
}

# Parse command line arguments
case "${1:-}" in
    "--help"|"-h")
        show_help
        exit 0
        ;;
    "--force"|"-f"|"true")
        FORCE_DEPLOY="true"
        ;;
    "")
        # No arguments, proceed normally
        ;;
    *)
        print_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac

# Run main deployment
main
