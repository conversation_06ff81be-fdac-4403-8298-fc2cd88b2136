#!/bin/bash

# Advanced Selective Deployment Script for ABRAAPI
# Uses Docker Compose profiles for more granular control

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Configuration
COMPOSE_FILES="-f docker-compose.yml -f docker-compose.selective.yml"
DEPLOYMENT_STATE_FILE=".deployment_state"

# Service to profile mapping
declare -A SERVICE_PROFILES=(
    ["assistant-service"]="assistant"
    ["auth-service"]="auth"
    ["marketdata-service"]="marketdata"
    ["thread-service"]="thread"
)

# Function to validate environment
validate_environment() {
    if [ ! -f ".env" ]; then
        print_error ".env file not found!"
        echo "Please create .env file from .env.example and configure your environment variables."
        exit 1
    fi

    # Load environment variables safely with better error handling
    print_status "Loading environment variables from .env file..."

    # First, check if .env file has basic syntax issues
    if ! grep -q "=" .env; then
        print_error ".env file appears to be empty or malformed"
        print_status "Showing .env file content:"
        cat .env
        exit 1
    fi

    # Load environment variables with explicit error checking
    set -a
    if ! source .env 2>/dev/null; then
        print_error "Failed to load .env file using 'source'. Trying alternative method..."

        # Alternative method: export each line manually
        while IFS= read -r line; do
            if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# && ! "$line" =~ ^[[:space:]]*$ ]]; then
                if [[ "$line" =~ ^[A-Za-z_][A-Za-z0-9_]*=.* ]]; then
                    export "$line"
                    print_status "Exported: $(echo "$line" | cut -d'=' -f1)"
                else
                    print_warning "Skipping malformed line: $line"
                fi
            fi
        done < .env
    fi
    set +a

    local required_vars=(
        "SUPABASE_CONNECTION_STRING"
        "SUPABASE_URL"
        "SUPABASE_JWT_SECRET"
        "POSTGRES_USER"
        "POSTGRES_PASSWORD"
        "POSTGRES_DB"
    )

    print_status "Validating required environment variables..."
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            print_error "Required environment variable $var is not set!"
            print_status "Available variables in .env:"
            grep -E "^[A-Z_]+" .env | cut -d'=' -f1 | head -10
            exit 1
        else
            print_status "✓ $var is set"
        fi
    done

    print_success "Environment variables validated"
}

# Function to detect changed services using the utility script
detect_changed_services() {
    local force_all=${1:-false}
    
    if [ "$force_all" = "true" ]; then
        ./scripts/detect-changes.sh --format list --force
    else
        ./scripts/detect-changes.sh --format list
    fi
}

# Function to build specific services using profiles
build_services() {
    local services_to_build=($@)
    
    if [ ${#services_to_build[@]} -eq 0 ]; then
        print_success "No services need to be built"
        return 0
    fi
    
    print_header "🔨 Building modified services..."
    
    # Enable Docker BuildKit for better performance
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    
    # Build services using profiles
    local profiles=()
    for service in "${services_to_build[@]}"; do
        if [[ -n "${SERVICE_PROFILES[$service]}" ]]; then
            profiles+=("${SERVICE_PROFILES[$service]}")
        fi
    done
    
    # Add database profile if marketdata service is being built
    if [[ " ${services_to_build[@]} " =~ " marketdata-service " ]]; then
        profiles+=("db")
    fi
    
    # Always include nginx profile
    profiles+=("nginx")
    
    # Remove duplicates
    local unique_profiles=($(printf "%s\n" "${profiles[@]}" | sort -u))
    
    print_status "Building profiles: ${unique_profiles[*]}"
    
    # Build with profiles
    local profile_args=""
    for profile in "${unique_profiles[@]}"; do
        profile_args="$profile_args --profile $profile"
    done
    
    if docker-compose $COMPOSE_FILES $profile_args build --parallel; then
        print_success "Services built successfully"
    else
        print_error "Failed to build services"
        exit 1
    fi
}

# Function to deploy services using profiles
deploy_services() {
    local services_to_deploy=($@)
    
    print_header "🚀 Deploying services..."
    
    # Determine profiles to deploy
    local profiles=()
    
    if [ ${#services_to_deploy[@]} -eq 0 ]; then
        # No specific services, just ensure everything is running
        profiles=("all")
    else
        # Add profiles for specific services
        for service in "${services_to_deploy[@]}"; do
            if [[ -n "${SERVICE_PROFILES[$service]}" ]]; then
                profiles+=("${SERVICE_PROFILES[$service]}")
            fi
        done
        
        # Add database profile if marketdata service is being deployed
        if [[ " ${services_to_deploy[@]} " =~ " marketdata-service " ]]; then
            profiles+=("db")
        fi
        
        # Always include nginx profile
        profiles+=("nginx")
    fi
    
    # Remove duplicates
    local unique_profiles=($(printf "%s\n" "${profiles[@]}" | sort -u))
    
    print_status "Deploying profiles: ${unique_profiles[*]}"
    
    # Deploy with profiles
    local profile_args=""
    for profile in "${unique_profiles[@]}"; do
        profile_args="$profile_args --profile $profile"
    done
    
    # Stop and remove only the services that are being redeployed
    if [ ${#services_to_deploy[@]} -gt 0 ]; then
        print_status "Stopping only the services that will be redeployed..."
        for service in "${services_to_deploy[@]}"; do
            print_status "Stopping $service..."
            docker-compose $COMPOSE_FILES stop "$service" 2>/dev/null || true
            docker-compose $COMPOSE_FILES rm -f "$service" 2>/dev/null || true
        done

        # Clean up only dangling images (not all images)
        print_status "Cleaning up dangling images..."
        docker image prune -f || true
    else
        print_status "No services to redeploy - all services will remain running"
    fi
    
    # Start services with profiles
    docker-compose $COMPOSE_FILES $profile_args up -d
}

# Function to wait for services to be healthy
wait_for_healthy_services() {
    print_header "⏳ Waiting for services to be healthy..."
    local timeout=300  # 5 minutes
    local elapsed=0
    local interval=10
    
    while [ $elapsed -lt $timeout ]; do
        local unhealthy_count=$(docker-compose $COMPOSE_FILES ps | grep -c "unhealthy\|starting" || echo "0")
        
        if [ "$unhealthy_count" -gt 0 ]; then
            print_status "Services still starting... (${elapsed}s/${timeout}s) - $unhealthy_count services not ready"
            sleep $interval
            elapsed=$((elapsed + interval))
        else
            print_success "All services are healthy!"
            return 0
        fi
    done
    
    print_error "Timeout waiting for services to be healthy"
    print_status "Showing logs for troubleshooting..."
    docker-compose $COMPOSE_FILES logs --tail=50
    exit 1
}

# Function to save deployment state
save_deployment_state() {
    git rev-parse HEAD > "$DEPLOYMENT_STATE_FILE"
    print_success "Deployment state saved: $(cat $DEPLOYMENT_STATE_FILE)"
}

# Function to show deployment summary
show_deployment_summary() {
    print_header "📊 Deployment Summary"
    
    echo "Service Status:"
    docker-compose $COMPOSE_FILES ps
    echo ""
    
    print_success "🎉 Deployment completed successfully!"
    echo ""
    echo "Services are available at:"
    echo "- Auth Service: http://localhost/auth"
    echo "- Thread Service: http://localhost/threads"
    echo "- Market Data Service: http://localhost/market"
    echo "- Assistant Service: http://localhost/assistant"
    echo ""
    
    local current_commit=$(git rev-parse --short HEAD)
    print_status "Deployed commit: $current_commit"
    print_status "Deployment timestamp: $(date)"
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS] [SERVICES...]"
    echo ""
    echo "Advanced selective deployment script using Docker Compose profiles."
    echo ""
    echo "Options:"
    echo "  --force, -f           Force deployment of all services"
    echo "  --auto, -a            Auto-detect changed services (default)"
    echo "  --help, -h            Show this help message"
    echo ""
    echo "Services:"
    echo "  assistant-service     Deploy Assistant Service"
    echo "  auth-service          Deploy Auth Service"
    echo "  marketdata-service    Deploy Market Data Service"
    echo "  thread-service        Deploy Thread Service"
    echo ""
    echo "Examples:"
    echo "  $0                              # Auto-detect and deploy changed services"
    echo "  $0 --force                      # Force deploy all services"
    echo "  $0 auth-service thread-service  # Deploy specific services"
    echo ""
}

# Main function
main() {
    local force_deploy=false
    local auto_detect=true
    local specific_services=()
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --force|-f)
                force_deploy=true
                auto_detect=false
                shift
                ;;
            --auto|-a)
                auto_detect=true
                shift
                ;;
            assistant-service|auth-service|marketdata-service|thread-service)
                specific_services+=("$1")
                auto_detect=false
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_header "🚀 Starting ABRAAPI Selective Deployment..."
    
    # Validate environment
    validate_environment
    
    # Determine services to deploy
    local services_to_deploy=()
    
    if [ "$force_deploy" = "true" ]; then
        services_to_deploy=("assistant-service" "auth-service" "marketdata-service" "thread-service")
        print_status "Force deploy mode - deploying all services"
    elif [ "$auto_detect" = "true" ]; then
        services_to_deploy=($(detect_changed_services))
        if [ ${#services_to_deploy[@]} -eq 0 ]; then
            print_success "No services have changed since last deployment"
        else
            print_header "📋 Auto-detected changed services:"
            for service in "${services_to_deploy[@]}"; do
                echo "  - $service"
            done
        fi
    else
        services_to_deploy=("${specific_services[@]}")
        print_header "📋 Manually specified services:"
        for service in "${services_to_deploy[@]}"; do
            echo "  - $service"
        done
    fi
    
    # Build and deploy
    build_services "${services_to_deploy[@]}"
    deploy_services "${services_to_deploy[@]}"
    wait_for_healthy_services
    save_deployment_state
    show_deployment_summary
}

# Run main function
main "$@"
