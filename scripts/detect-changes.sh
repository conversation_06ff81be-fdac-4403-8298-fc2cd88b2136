#!/bin/bash

# Service Change Detection Utility for ABRAAPI
# This script detects which services have changed since the last deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1" >&2
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" >&2
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

print_header() {
    echo -e "${CYAN}$1${NC}" >&2
}

# Configuration
DEPLOYMENT_STATE_FILE=".deployment_state"
OUTPUT_FORMAT="human"  # human, json, list

# Service definitions
declare -A SERVICES=(
    ["assistant-service"]="AssistantService"
    ["auth-service"]="AuthService"
    ["marketdata-service"]="MarketDataService"
    ["thread-service"]="ThreadService"
)

# Function to get last deployment commit
get_last_deployment_commit() {
    if [ -f "$DEPLOYMENT_STATE_FILE" ]; then
        cat "$DEPLOYMENT_STATE_FILE"
    else
        echo ""
    fi
}

# Function to detect changed services
detect_changed_services() {
    local last_commit=$(get_last_deployment_commit)
    local changed_services=()
    local force_all=${1:-false}
    
    if [ -z "$last_commit" ] || [ "$force_all" = "true" ]; then
        if [ "$OUTPUT_FORMAT" = "human" ]; then
            if [ -z "$last_commit" ]; then
                print_warning "No previous deployment state found"
            else
                print_warning "Force mode enabled"
            fi
            print_status "All services will be considered changed"
        fi
        for service in "${!SERVICES[@]}"; do
            changed_services+=("$service")
        done
    else
        if [ "$OUTPUT_FORMAT" = "human" ]; then
            print_status "Checking for changes since commit: $last_commit"
        fi
        
        # Check if the commit exists
        if ! git cat-file -e "$last_commit" 2>/dev/null; then
            if [ "$OUTPUT_FORMAT" = "human" ]; then
                print_warning "Last deployment commit not found in current repository"
                print_status "All services will be considered changed"
            fi
            for service in "${!SERVICES[@]}"; do
                changed_services+=("$service")
            done
        else
            # Check for changes in each service directory
            for service in "${!SERVICES[@]}"; do
                local service_dir="${SERVICES[$service]}"
                if git diff --quiet "$last_commit" HEAD -- "$service_dir/"; then
                    if [ "$OUTPUT_FORMAT" = "human" ]; then
                        print_status "No changes detected in $service ($service_dir)"
                    fi
                else
                    if [ "$OUTPUT_FORMAT" = "human" ]; then
                        print_status "Changes detected in $service ($service_dir)"
                    fi
                    changed_services+=("$service")
                fi
            done
            
            # Check for changes in infrastructure files
            local infra_changed=false
            for file in "docker-compose.yml" "nginx/" "scripts/"; do
                if ! git diff --quiet "$last_commit" HEAD -- "$file"; then
                    infra_changed=true
                    break
                fi
            done
            
            if [ "$infra_changed" = "true" ]; then
                if [ "$OUTPUT_FORMAT" = "human" ]; then
                    print_warning "Infrastructure changes detected - all services affected"
                fi
                changed_services=()
                for service in "${!SERVICES[@]}"; do
                    changed_services+=("$service")
                done
            fi
        fi
    fi
    
    echo "${changed_services[@]}"
}

# Function to get detailed change information
get_change_details() {
    local last_commit=$(get_last_deployment_commit)
    local service=$1
    local service_dir="${SERVICES[$service]}"
    
    if [ -z "$last_commit" ]; then
        echo "No previous deployment state"
        return
    fi
    
    if ! git cat-file -e "$last_commit" 2>/dev/null; then
        echo "Previous deployment commit not found"
        return
    fi
    
    echo "Changes in $service_dir since $last_commit:"
    git diff --stat "$last_commit" HEAD -- "$service_dir/"
    echo ""
    echo "Modified files:"
    git diff --name-only "$last_commit" HEAD -- "$service_dir/"
}

# Function to output results in different formats
output_results() {
    local changed_services=($@)
    
    case "$OUTPUT_FORMAT" in
        "json")
            echo -n "{"
            echo -n "\"changed_services\":["
            for i in "${!changed_services[@]}"; do
                if [ $i -gt 0 ]; then echo -n ","; fi
                echo -n "\"${changed_services[$i]}\""
            done
            echo -n "],"
            echo -n "\"last_commit\":\"$(get_last_deployment_commit)\","
            echo -n "\"current_commit\":\"$(git rev-parse HEAD)\","
            echo -n "\"timestamp\":\"$(date -Iseconds)\""
            echo "}"
            ;;
        "list")
            for service in "${changed_services[@]}"; do
                echo "$service"
            done
            ;;
        "human"|*)
            if [ ${#changed_services[@]} -eq 0 ]; then
                print_success "No services have changed since last deployment"
            else
                print_header "📋 Changed Services:"
                for service in "${changed_services[@]}"; do
                    echo "  - $service (${SERVICES[$service]})"
                done
                echo ""
                print_status "Total changed services: ${#changed_services[@]}"
            fi
            
            local current_commit=$(git rev-parse --short HEAD)
            local last_commit=$(get_last_deployment_commit)
            if [ -n "$last_commit" ]; then
                print_status "Last deployment: $(git log --format='%h - %s (%cr)' -n 1 $last_commit 2>/dev/null || echo $last_commit)"
            fi
            print_status "Current commit: $(git log --format='%h - %s (%cr)' -n 1 $current_commit)"
            ;;
    esac
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Detect which services have changed since the last deployment."
    echo ""
    echo "Options:"
    echo "  --force, -f           Consider all services as changed"
    echo "  --format FORMAT       Output format: human (default), json, list"
    echo "  --details SERVICE     Show detailed changes for a specific service"
    echo "  --help, -h            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Show changed services in human-readable format"
    echo "  $0 --format json      # Output as JSON"
    echo "  $0 --format list      # Output as simple list"
    echo "  $0 --details auth-service  # Show detailed changes for auth service"
    echo ""
}

# Parse command line arguments
FORCE_ALL=false
SHOW_DETAILS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            exit 0
            ;;
        --force|-f)
            FORCE_ALL=true
            shift
            ;;
        --format)
            OUTPUT_FORMAT="$2"
            shift 2
            ;;
        --details)
            SHOW_DETAILS="$2"
            shift 2
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate format
case "$OUTPUT_FORMAT" in
    "human"|"json"|"list")
        ;;
    *)
        print_error "Invalid format: $OUTPUT_FORMAT. Use: human, json, or list"
        exit 1
        ;;
esac

# Main execution
if [ -n "$SHOW_DETAILS" ]; then
    if [[ ! " ${!SERVICES[@]} " =~ " $SHOW_DETAILS " ]]; then
        print_error "Unknown service: $SHOW_DETAILS"
        print_status "Available services: ${!SERVICES[@]}"
        exit 1
    fi
    get_change_details "$SHOW_DETAILS"
else
    # Get changed services as a string and convert to array properly
    changed_services_str=$(detect_changed_services "$FORCE_ALL")
    if [ -n "$changed_services_str" ]; then
        read -ra changed_services <<< "$changed_services_str"
    else
        changed_services=()
    fi
    output_results "${changed_services[@]}"
fi
