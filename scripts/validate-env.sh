#!/bin/bash

# Environment File Validation Script for ABRAAPI
# Validates .env file syntax and required variables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Required environment variables
REQUIRED_VARS=(
    "SUPABASE_CONNECTION_STRING"
    "SUPABASE_URL"
    "SUPABASE_JWT_SECRET"
    "POSTGRES_USER"
    "POSTGRES_PASSWORD"
    "POSTGRES_DB"
)

# Function to validate .env file syntax
validate_syntax() {
    local env_file=${1:-.env}
    
    if [ ! -f "$env_file" ]; then
        print_error ".env file not found: $env_file"
        return 1
    fi
    
    print_header "🔍 Validating .env file syntax..."
    
    # Try to source the file in a subshell to catch syntax errors
    if (set -a && source "$env_file" && set +a) 2>/dev/null; then
        print_success ".env file syntax is valid"
        return 0
    else
        print_error ".env file has syntax errors"
        
        # Try to identify problematic lines
        print_status "Checking for common issues..."
        
        local line_num=1
        while IFS= read -r line; do
            # Skip empty lines and comments
            if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
                ((line_num++))
                continue
            fi
            
            # Check for unquoted values with special characters
            if [[ "$line" =~ ^[A-Z_]+=.*[^a-zA-Z0-9._/-] ]] && [[ ! "$line" =~ ^[A-Z_]+=\".*\"$ ]]; then
                print_warning "Line $line_num may need quotes: $line"
            fi
            
            # Check for missing equals sign
            if [[ "$line" =~ ^[A-Z_]+[^=] ]]; then
                print_warning "Line $line_num missing equals sign: $line"
            fi
            
            ((line_num++))
        done < "$env_file"
        
        return 1
    fi
}

# Function to validate required variables
validate_required_vars() {
    local env_file=${1:-.env}
    
    print_header "🔍 Validating required environment variables..."
    
    # Load environment variables
    if ! (set -a && source "$env_file" && set +a) 2>/dev/null; then
        print_error "Cannot load .env file due to syntax errors"
        return 1
    fi
    
    # Source the file to get variables
    set -a
    source "$env_file"
    set +a
    
    local missing_vars=()
    local empty_vars=()
    
    for var in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!var+x}" ]; then
            missing_vars+=("$var")
        elif [ -z "${!var}" ]; then
            empty_vars+=("$var")
        else
            print_success "$var is set"
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        print_error "Missing required variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
    fi
    
    if [ ${#empty_vars[@]} -gt 0 ]; then
        print_warning "Empty required variables:"
        for var in "${empty_vars[@]}"; do
            echo "  - $var"
        done
    fi
    
    if [ ${#missing_vars[@]} -eq 0 ] && [ ${#empty_vars[@]} -eq 0 ]; then
        print_success "All required variables are set"
        return 0
    else
        return 1
    fi
}

# Function to show environment info
show_env_info() {
    local env_file=${1:-.env}
    
    print_header "📊 Environment File Information"
    echo ""
    
    if [ ! -f "$env_file" ]; then
        print_error ".env file not found: $env_file"
        return 1
    fi
    
    print_status "File: $env_file"
    print_status "Size: $(wc -c < "$env_file") bytes"
    print_status "Lines: $(wc -l < "$env_file")"
    
    echo ""
    print_status "Variables found:"
    grep -E "^[A-Z_]+=" "$env_file" | cut -d'=' -f1 | sort | while read -r var; do
        echo "  - $var"
    done
    
    echo ""
    print_status "Comments and empty lines:"
    local comments=$(grep -c "^#" "$env_file" || echo "0")
    local empty=$(grep -c "^$" "$env_file" || echo "0")
    echo "  - Comments: $comments"
    echo "  - Empty lines: $empty"
}

# Function to fix common issues
fix_env_file() {
    local env_file=${1:-.env}
    local backup_file="${env_file}.backup.$(date +%s)"
    
    if [ ! -f "$env_file" ]; then
        print_error ".env file not found: $env_file"
        return 1
    fi
    
    print_header "🔧 Attempting to fix common .env issues..."
    
    # Create backup
    cp "$env_file" "$backup_file"
    print_status "Backup created: $backup_file"
    
    # Fix unquoted values with special characters
    print_status "Adding quotes to values with special characters..."
    
    while IFS= read -r line; do
        # Skip empty lines and comments
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            echo "$line"
            continue
        fi
        
        # Check if line has format VAR=value and value contains special chars but isn't quoted
        if [[ "$line" =~ ^([A-Z_]+)=(.*)$ ]]; then
            local var_name="${BASH_REMATCH[1]}"
            local var_value="${BASH_REMATCH[2]}"
            
            # If value contains special characters and isn't already quoted
            if [[ "$var_value" =~ [^a-zA-Z0-9._/-] ]] && [[ ! "$var_value" =~ ^\".*\"$ ]]; then
                echo "${var_name}=\"${var_value}\""
            else
                echo "$line"
            fi
        else
            echo "$line"
        fi
    done < "$backup_file" > "$env_file"
    
    print_success "Fixed .env file"
    print_status "Original backed up to: $backup_file"
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS] [ENV_FILE]"
    echo ""
    echo "Validate .env file syntax and required variables."
    echo ""
    echo "Options:"
    echo "  --syntax, -s      Validate syntax only"
    echo "  --vars, -v        Validate required variables only"
    echo "  --info, -i        Show environment file information"
    echo "  --fix, -f         Attempt to fix common issues"
    echo "  --help, -h        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                # Validate default .env file"
    echo "  $0 --syntax       # Check syntax only"
    echo "  $0 --fix          # Fix common issues"
    echo "  $0 production.env # Validate specific file"
    echo ""
}

# Parse command line arguments
ACTION="all"
ENV_FILE=".env"

while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            exit 0
            ;;
        --syntax|-s)
            ACTION="syntax"
            shift
            ;;
        --vars|-v)
            ACTION="vars"
            shift
            ;;
        --info|-i)
            ACTION="info"
            shift
            ;;
        --fix|-f)
            ACTION="fix"
            shift
            ;;
        -*)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            ENV_FILE="$1"
            shift
            ;;
    esac
done

# Execute the requested action
case "$ACTION" in
    "syntax")
        validate_syntax "$ENV_FILE"
        ;;
    "vars")
        validate_required_vars "$ENV_FILE"
        ;;
    "info")
        show_env_info "$ENV_FILE"
        ;;
    "fix")
        fix_env_file "$ENV_FILE"
        ;;
    "all")
        show_env_info "$ENV_FILE"
        echo ""
        if validate_syntax "$ENV_FILE"; then
            echo ""
            validate_required_vars "$ENV_FILE"
        fi
        ;;
    *)
        print_error "Invalid action: $ACTION"
        show_help
        exit 1
        ;;
esac
