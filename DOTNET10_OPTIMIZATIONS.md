# .NET 10 Docker Optimizations

This document explains the .NET 10 specific optimizations implemented in the Dockerfiles for the Abra API services.

## Overview

All services have been optimized for .NET 10 preview with the latest performance enhancements, security improvements, and build optimizations.

## Key Optimizations

### 1. Build Stage Optimizations

#### .NET 10 Performance Environment Variables
```dockerfile
ENV DOTNET_EnableWriteXorExecute=0      # Disable W^X for better performance
ENV DOTNET_TieredPGO=1                  # Enable Tiered Profile Guided Optimization
ENV DOTNET_TC_QuickJitForLoops=1        # Quick JIT for loops
ENV DOTNET_ReadyToRun=0                 # Disable ReadyToRun for smaller images
```

#### Enhanced NuGet Caching
```dockerfile
RUN mkdir -p /root/.nuget/packages && \
    mkdir -p /tmp/nuget-scratch
```

#### Optimized Package Restore
```dockerfile
RUN dotnet restore "ServiceName.csproj" \
        --verbosity minimal \
        --runtime linux-musl-x64 \
        --packages /root/.nuget/packages
```

#### Advanced Publishing Options
```dockerfile
RUN dotnet publish "ServiceName.csproj" \
    -c Release \
    -o /app \
    --no-restore \
    --verbosity minimal \
    --runtime linux-musl-x64 \
    --self-contained false \
    -p:PublishReadyToRun=false \
    -p:PublishSingleFile=false \
    -p:PublishTrimmed=false
```

### 2. Runtime Stage Optimizations

#### .NET 10 Runtime Environment Variables
```dockerfile
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1  # Invariant globalization for smaller footprint
ENV DOTNET_EnableDiagnostics=0               # Disable diagnostics in production
ENV DOTNET_TieredPGO=1                       # Enable Tiered PGO at runtime
ENV DOTNET_TC_QuickJitForLoops=1             # Quick JIT for loops
ENV DOTNET_ReadyToRun=0                      # Disable ReadyToRun
ENV ASPNETCORE_URLS=http://+:8080            # Explicit URL binding
```

#### Enhanced Security
- Non-root user execution (appuser:appgroup)
- Proper file ownership and permissions
- Minimal Alpine base image
- Essential packages only

#### Optimized Health Checks
```dockerfile
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1
```

### 3. .dockerignore Optimizations

Enhanced .dockerignore files exclude .NET 10 specific artifacts:

```dockerignore
# .NET 10 Build artifacts
bin/
obj/
out/
publish/
artifacts/

# .NET 10 specific cache and temp directories
.dotnet/
.nuget/
packages/
TestResults/
BenchmarkDotNet.Artifacts/

# .NET 10 specific runtime files
*.runtimeconfig.json
*.deps.json
```

## Performance Benefits

### 1. Tiered Profile Guided Optimization (PGO)
- **DOTNET_TieredPGO=1**: Enables dynamic optimization based on runtime behavior
- Improves performance for hot code paths
- Reduces memory allocation and GC pressure

### 2. Quick JIT for Loops
- **DOTNET_TC_QuickJitForLoops=1**: Faster compilation of loop-heavy code
- Reduces startup time for services with intensive loops
- Better performance for data processing operations

### 3. Optimized Memory Management
- **DOTNET_EnableWriteXorExecute=0**: Disables W^X protection for better performance
- **DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1**: Reduces memory footprint
- **DOTNET_EnableDiagnostics=0**: Disables diagnostic overhead in production

### 4. Build Optimizations
- **Runtime-specific restore**: `--runtime linux-musl-x64` for Alpine compatibility
- **Disabled ReadyToRun**: Smaller image size, faster startup in containers
- **Optimized publishing**: No single-file or trimming for better compatibility

## Security Enhancements

### 1. Non-Root Execution
```dockerfile
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup
USER appuser
```

### 2. Minimal Attack Surface
- Alpine Linux base image
- Only essential packages installed
- No unnecessary tools or utilities

### 3. Proper File Permissions
```dockerfile
COPY --from=build --chown=appuser:appgroup /app .
```

## Monitoring and Diagnostics

### 1. Health Check Configuration
- **Interval**: 30 seconds (balanced monitoring)
- **Timeout**: 10 seconds (adequate for .NET 10 startup)
- **Start Period**: 60 seconds (allows for .NET 10 initialization)
- **Retries**: 3 attempts before marking unhealthy

### 2. Logging Directory
```dockerfile
RUN mkdir -p /app/logs && \
    chown -R appuser:appgroup /app
```

## Compatibility Notes

### 1. Alpine Linux Compatibility
- Uses `linux-musl-x64` runtime for Alpine compatibility
- Includes `ca-certificates` for HTTPS operations
- Includes `tzdata` for timezone operations

### 2. .NET 10 Preview Considerations
- Uses `mcr.microsoft.com/dotnet/sdk:10.0-preview` for build
- Uses `mcr.microsoft.com/dotnet/aspnet:10.0-preview-alpine` for runtime
- Will be updated to stable versions when .NET 10 is released

## Best Practices

### 1. Layer Caching
- Project files copied separately for better caching
- Dependencies restored before source code copy
- Multi-stage builds for optimal layer reuse

### 2. Build Efficiency
- Minimal verbosity for faster builds
- No-restore publishing to avoid duplicate work
- Optimized NuGet package caching

### 3. Production Readiness
- Disabled development features
- Optimized for production workloads
- Security-first configuration

## Migration from Previous Versions

### Changes from .NET 8/9
1. **New Environment Variables**: Added .NET 10 specific optimizations
2. **Enhanced PGO**: Improved profile-guided optimization
3. **Better Alpine Support**: Optimized for Alpine Linux containers
4. **Security Improvements**: Enhanced non-root execution

### Backward Compatibility
- All existing APIs remain compatible
- Configuration patterns unchanged
- Deployment processes remain the same

## Troubleshooting

### Common Issues
1. **Startup Time**: Increased start period to 60s for .NET 10 initialization
2. **Memory Usage**: Monitor with new PGO optimizations
3. **Alpine Compatibility**: Ensure musl runtime compatibility

### Performance Monitoring
- Use health checks to monitor service availability
- Monitor memory usage with new optimizations
- Track startup times with .NET 10 improvements

## Future Considerations

### .NET 10 Stable Release
- Update base images when stable versions are available
- Review and optimize new .NET 10 features
- Update documentation with stable version specifics

### Performance Tuning
- Monitor PGO effectiveness in production
- Adjust environment variables based on workload
- Consider ReadyToRun for specific scenarios
