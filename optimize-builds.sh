#!/bin/bash

# Docker build optimization script for .NET 10 services
# Improves build performance and reduces build times

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[BUILD-OPT]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Set optimal Docker BuildKit settings
setup_buildkit() {
    log "Setting up Docker BuildKit optimizations..."
    
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    export BUILDKIT_PROGRESS=plain
    
    # Optimize BuildKit cache
    export BUILDKIT_CACHE_MOUNT_NS=abraapi
    
    success "BuildKit optimizations enabled"
}

# Clean up build cache strategically
optimize_cache() {
    log "Optimizing Docker build cache..."
    
    # Remove only dangling images and build cache older than 24h
    docker image prune -f --filter "until=24h" || true
    docker builder prune -f --filter "until=24h" || true
    
    # Keep base images for faster rebuilds
    log "Preserving .NET 10 base images for faster rebuilds..."
    docker pull mcr.microsoft.com/dotnet/sdk:10.0-preview || true
    docker pull mcr.microsoft.com/dotnet/aspnet:10.0-preview-alpine || true
    
    success "Cache optimization completed"
}

# Set system-specific optimizations
set_system_optimizations() {
    log "Applying system-specific optimizations..."
    
    # Increase Docker daemon limits
    if command -v docker &> /dev/null; then
        # Set memory limits for builds
        export DOCKER_DEFAULT_PLATFORM=linux/amd64
        
        # Optimize for available CPU cores
        local cpu_cores=$(nproc 2>/dev/null || echo "4")
        export DOCKER_BUILDKIT_STEP_LOG_MAX_SIZE=50000000
        export DOCKER_BUILDKIT_STEP_LOG_MAX_SPEED=100000000
        
        log "Optimized for $cpu_cores CPU cores"
    fi
    
    success "System optimizations applied"
}

# Build services with optimizations
build_optimized() {
    local compose_file=${1:-"docker-compose.green.yml"}
    
    log "Building services with optimizations using $compose_file..."
    
    # Build with parallel processing and optimized settings
    docker compose -f "$compose_file" build \
        --parallel \
        --progress=plain \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --build-arg DOCKER_BUILDKIT=1
    
    success "Optimized build completed"
}

# Monitor build progress
monitor_build() {
    local compose_file=${1:-"docker-compose.green.yml"}
    
    log "Starting monitored build process..."
    
    # Start build in background and monitor
    docker compose -f "$compose_file" build --parallel --progress=plain &
    local build_pid=$!
    
    # Monitor system resources during build
    while kill -0 $build_pid 2>/dev/null; do
        echo "Build progress - Memory: $(free -h | awk '/^Mem:/ {print $3 "/" $2}'), Disk: $(df -h / | awk 'NR==2 {print $3 "/" $2}')"
        sleep 30
    done
    
    wait $build_pid
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        success "Monitored build completed successfully"
    else
        error "Build failed with exit code $exit_code"
        return $exit_code
    fi
}

# Pre-warm NuGet cache
prewarm_nuget_cache() {
    log "Pre-warming NuGet cache for faster builds..."
    
    # Create a temporary project to download common packages
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # Create a minimal project with common dependencies
    cat > temp.csproj << 'EOF'
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <RuntimeIdentifier>linux-musl-x64</RuntimeIdentifier>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="System.Text.Json" />
  </ItemGroup>
</Project>
EOF
    
    # Restore packages to warm cache
    docker run --rm -v "$(pwd):/src" -w /src \
        mcr.microsoft.com/dotnet/sdk:10.0-preview \
        dotnet restore temp.csproj --runtime linux-musl-x64 || true
    
    cd - > /dev/null
    rm -rf "$temp_dir"
    
    success "NuGet cache pre-warmed"
}

# Show build statistics
show_build_stats() {
    log "Build statistics:"
    
    echo "Docker images:"
    docker images | grep -E "(abraapi|assistant-service|auth-service|marketdata-service|thread-service)" | head -10
    
    echo ""
    echo "Build cache usage:"
    docker system df
    
    echo ""
    echo "System resources:"
    free -h
    df -h /
}

# Main optimization function
optimize_all() {
    local compose_file=${1:-"docker-compose.green.yml"}
    local skip_prewarm=${2:-false}
    
    log "Starting comprehensive build optimization..."
    
    setup_buildkit
    set_system_optimizations
    optimize_cache
    
    if [ "$skip_prewarm" != "true" ]; then
        prewarm_nuget_cache
    fi
    
    build_optimized "$compose_file"
    show_build_stats
    
    success "All optimizations completed!"
}

# Quick optimization (skip pre-warming)
optimize_quick() {
    local compose_file=${1:-"docker-compose.green.yml"}
    
    log "Starting quick build optimization..."
    
    setup_buildkit
    set_system_optimizations
    build_optimized "$compose_file"
    
    success "Quick optimization completed!"
}

# Main script logic
case "${1:-optimize}" in
    "optimize")
        compose_file=${2:-"docker-compose.green.yml"}
        optimize_all "$compose_file"
        ;;
    "quick")
        compose_file=${2:-"docker-compose.green.yml"}
        optimize_quick "$compose_file"
        ;;
    "monitor")
        compose_file=${2:-"docker-compose.green.yml"}
        setup_buildkit
        set_system_optimizations
        monitor_build "$compose_file"
        ;;
    "cache")
        optimize_cache
        ;;
    "prewarm")
        prewarm_nuget_cache
        ;;
    "stats")
        show_build_stats
        ;;
    *)
        echo "Usage: $0 {optimize|quick|monitor|cache|prewarm|stats} [compose-file]"
        echo "  optimize [file]  - Full optimization with pre-warming (default: docker-compose.green.yml)"
        echo "  quick [file]     - Quick optimization without pre-warming"
        echo "  monitor [file]   - Build with resource monitoring"
        echo "  cache            - Optimize Docker cache only"
        echo "  prewarm          - Pre-warm NuGet cache only"
        echo "  stats            - Show build statistics"
        exit 1
        ;;
esac
