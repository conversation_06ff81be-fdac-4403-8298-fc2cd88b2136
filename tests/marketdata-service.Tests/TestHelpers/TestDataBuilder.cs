using MarketDataService.Models;

namespace MarketDataService.Tests.TestHelpers;

public static class TestDataBuilder
{
    public static MarketData CreateMarketData(
        string symbol = "AAPL",
        decimal price = 150.00m,
        DateTime? timestamp = null)
    {
        return new MarketData
        {
            Id = 1,
            Symbol = symbol,
            Price = price,
            Timestamp = timestamp ?? DateTime.UtcNow
        };
    }

    public static BrokerMetadata CreateBrokerMetadata(
        string id = "test-broker",
        string name = "Test Broker",
        string status = "online")
    {
        return new BrokerMetadata
        {
            Id = id,
            Name = name,
            Status = status,
            SupportedAssets = new List<string> { "stocks", "crypto" },
            DocsUrl = "https://test-broker.com/docs"
        };
    }

    public static HistoricalPrice CreateHistoricalPrice(
        DateTime? date = null,
        decimal open = 100m,
        decimal high = 110m,
        decimal low = 95m,
        decimal close = 105m,
        long volume = 1000000)
    {
        return new HistoricalPrice
        {
            Date = date ?? DateTime.UtcNow.Date,
            Open = open,
            High = high,
            Low = low,
            Close = close,
            Volume = volume
        };
    }
}
