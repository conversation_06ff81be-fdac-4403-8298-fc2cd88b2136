using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Tests.TestHelpers;

public class MockPriceProvider : IPriceProvider
{
    public string BrokerId { get; set; } = "mock-broker";
    public decimal? PriceToReturn { get; set; }
    public bool ShouldThrow { get; set; }
    public Exception? ExceptionToThrow { get; set; }

    public Task<decimal?> FetchPriceAsync(string symbol)
    {
        if (ShouldThrow)
        {
            throw ExceptionToThrow ?? new InvalidOperationException("Mock exception");
        }
        return Task.FromResult(PriceToReturn);
    }
}

public class MockHistoricalPriceProvider : IHistoricalPriceProvider
{
    public string BrokerId { get; set; } = "mock-broker";
    public List<HistoricalPrice>? DataToReturn { get; set; }
    public bool ShouldThrow { get; set; }
    public Exception? ExceptionToThrow { get; set; }

    public Task<List<HistoricalPrice>?> GetHistoricalPricesAsync(string symbol, DateTime from, DateTime to, string interval)
    {
        if (ShouldThrow)
        {
            throw ExceptionToThrow ?? new InvalidOperationException("Mock exception");
        }
        return Task.FromResult(DataToReturn);
    }
}

public class MockBrokerMetadataProvider : IBrokerMetadataProvider
{
    public string BrokerId { get; set; } = "mock-broker";
    public BrokerMetadata? MetadataToReturn { get; set; }

    public BrokerMetadata GetMetadata()
    {
        return MetadataToReturn ?? TestDataBuilder.CreateBrokerMetadata(BrokerId);
    }
}

public class MockSymbolProvider : ISymbolProvider
{
    public string BrokerId { get; set; } = "mock-broker";
    public List<Symbol> AllSymbolsToReturn { get; set; } = new();
    public List<Symbol> MarketTypeSymbolsToReturn { get; set; } = new();
    public List<ActiveSymbol> ActiveSymbolsToReturn { get; set; } = new();
    public List<SymbolSearchResult> SearchResultsToReturn { get; set; } = new();
    public bool ShouldThrow { get; set; }
    public Exception? ExceptionToThrow { get; set; }

    public Task<List<Symbol>> GetAllSymbolsAsync()
    {
        if (ShouldThrow)
        {
            throw ExceptionToThrow ?? new InvalidOperationException("Mock exception");
        }
        return Task.FromResult(AllSymbolsToReturn);
    }

    public Task<List<Symbol>> GetSymbolsByMarketTypeAsync(string marketType)
    {
        if (ShouldThrow)
        {
            throw ExceptionToThrow ?? new InvalidOperationException("Mock exception");
        }
        return Task.FromResult(MarketTypeSymbolsToReturn.Where(s => s.MarketType == marketType).ToList());
    }

    public Task<List<ActiveSymbol>> GetMostActiveSymbolsAsync(string? marketType = null, int limit = 50)
    {
        if (ShouldThrow)
        {
            throw ExceptionToThrow ?? new InvalidOperationException("Mock exception");
        }
        var filtered = ActiveSymbolsToReturn.AsEnumerable();
        if (marketType != null)
        {
            filtered = filtered.Where(s => s.MarketType == marketType);
        }
        return Task.FromResult(filtered.Take(limit).ToList());
    }

    public Task<List<SymbolSearchResult>> SearchSymbolsAsync(string query, string? marketType = null, int limit = 20)
    {
        if (ShouldThrow)
        {
            throw ExceptionToThrow ?? new InvalidOperationException("Mock exception");
        }
        var filtered = SearchResultsToReturn.AsEnumerable();
        if (marketType != null)
        {
            filtered = filtered.Where(s => s.MarketType == marketType);
        }
        return Task.FromResult(filtered.Take(limit).ToList());
    }
}
