using FluentAssertions;
using MarketDataService.Controllers;
using MarketDataService.Interfaces;
using MarketDataService.Models;
using MarketDataService.Tests.TestHelpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;
using Xunit;

namespace MarketDataService.Tests.Controllers;

public class MarketDataControllerTests
{
    private readonly Mock<IPriceService> _mockPriceService;
    private readonly Mock<IHistoricalPriceRegistry> _mockHistoryRegistry;
    private readonly Mock<ILogger<MarketDataController>> _mockLogger;
    private readonly MarketDataController _controller;

    public MarketDataControllerTests()
    {
        _mockPriceService = new Mock<IPriceService>();
        _mockHistoryRegistry = new Mock<IHistoricalPriceRegistry>();
        _mockLogger = new Mock<ILogger<MarketDataController>>();
        _controller = new MarketDataController(_mockPriceService.Object, _mockHistoryRegistry.Object, _mockLogger.Object);
    }

    private void SetupUserClaims(string brokerId)
    {
        var claims = new List<Claim>();

        // Only add broker_id claim if it's not null or empty
        if (!string.IsNullOrEmpty(brokerId))
        {
            claims.Add(new("broker_id", brokerId));
        }

        var identity = new ClaimsIdentity(claims, "test");
        var principal = new ClaimsPrincipal(identity);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext
            {
                User = principal
            }
        };
    }

    [Fact]
    public async Task FetchLive_WithValidBrokerIdAndSuccessfulFetch_ShouldReturnOk()
    {
        // Arrange
        var symbol = "AAPL";
        var brokerId = "finnhub";
        var marketData = TestDataBuilder.CreateMarketData(symbol, 150.00m);
        
        SetupUserClaims(brokerId);
        _mockPriceService.Setup(s => s.GetLatestPriceAsync(symbol, brokerId, false))
                        .ReturnsAsync(marketData);

        // Act
        var result = await _controller.FetchLive(symbol);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().Be(marketData);
    }

    [Fact]
    public async Task FetchLive_WithNoCacheFlag_ShouldBypassCache()
    {
        // Arrange
        var symbol = "AAPL";
        var brokerId = "finnhub";
        var marketData = TestDataBuilder.CreateMarketData(symbol, 150.00m);
        
        SetupUserClaims(brokerId);
        _mockPriceService.Setup(s => s.GetLatestPriceAsync(symbol, brokerId, true))
                        .ReturnsAsync(marketData);

        // Act
        var result = await _controller.FetchLive(symbol, nocache: true);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        _mockPriceService.Verify(s => s.GetLatestPriceAsync(symbol, brokerId, true), Times.Once);
    }

    [Fact]
    public async Task FetchLive_WithMissingBrokerId_ShouldUseDefaultBroker()
    {
        // Arrange
        var symbol = "AAPL";
        var marketData = TestDataBuilder.CreateMarketData(symbol, 150.00m);
        SetupUserClaims(""); // Empty broker ID

        // Setup mock to expect default broker "finnhub"
        _mockPriceService.Setup(s => s.GetLatestPriceAsync(symbol, "finnhub", false))
                        .ReturnsAsync(marketData);

        // Act
        var result = await _controller.FetchLive(symbol);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().Be(marketData);

        // Verify that the default broker was used
        _mockPriceService.Verify(s => s.GetLatestPriceAsync(symbol, "finnhub", false), Times.Once);
    }

    [Fact]
    public async Task FetchLive_WithNoBrokerIdClaim_ShouldUseDefaultBroker()
    {
        // Arrange
        var symbol = "AAPL";
        var marketData = TestDataBuilder.CreateMarketData(symbol, 150.00m);

        // Setup user claims without broker_id claim at all
        var claims = new List<Claim>
        {
            new("sub", "test-user-id"),
            new("email", "<EMAIL>")
        };
        var identity = new ClaimsIdentity(claims, "test");
        var principal = new ClaimsPrincipal(identity);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext
            {
                User = principal
            }
        };

        // Setup mock to expect default broker "finnhub"
        _mockPriceService.Setup(s => s.GetLatestPriceAsync(symbol, "finnhub", false))
                        .ReturnsAsync(marketData);

        // Act
        var result = await _controller.FetchLive(symbol);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().Be(marketData);

        // Verify that the default broker was used
        _mockPriceService.Verify(s => s.GetLatestPriceAsync(symbol, "finnhub", false), Times.Once);
    }

    [Fact]
    public async Task FetchLive_WithFailedFetch_ShouldReturnProblem()
    {
        // Arrange
        var symbol = "AAPL";
        var brokerId = "finnhub";
        
        SetupUserClaims(brokerId);
        _mockPriceService.Setup(s => s.GetLatestPriceAsync(symbol, brokerId, false))
                        .ReturnsAsync((MarketData?)null);

        // Act
        var result = await _controller.FetchLive(symbol);

        // Assert
        result.Should().BeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task History_WithValidParameters_ShouldReturnOk()
    {
        // Arrange
        var symbol = "AAPL";
        var brokerId = "finnhub";
        var from = DateTime.UtcNow.AddDays(-30);
        var to = DateTime.UtcNow;
        var interval = "1d";
        var historicalData = new List<HistoricalPrice>
        {
            TestDataBuilder.CreateHistoricalPrice(from.AddDays(1)),
            TestDataBuilder.CreateHistoricalPrice(from.AddDays(2))
        };
        
        var mockProvider = new MockHistoricalPriceProvider 
        { 
            BrokerId = brokerId, 
            DataToReturn = historicalData 
        };
        
        SetupUserClaims(brokerId);
        _mockHistoryRegistry.Setup(r => r.GetProvider(brokerId))
                          .Returns(mockProvider);

        // Act
        var result = await _controller.History(symbol, from, to, interval);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().Be(historicalData);
    }

    [Fact]
    public async Task History_WithNoProvider_ShouldReturnBadRequest()
    {
        // Arrange
        var symbol = "AAPL";
        var brokerId = "unknown";
        var from = DateTime.UtcNow.AddDays(-30);
        var to = DateTime.UtcNow;
        
        SetupUserClaims(brokerId);
        _mockHistoryRegistry.Setup(r => r.GetProvider(brokerId))
                          .Returns((MockHistoricalPriceProvider?)null);

        // Act
        var result = await _controller.History(symbol, from, to);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().Be($"No provider for broker '{brokerId}'");
    }

    [Fact]
    public async Task History_WithFailedFetch_ShouldReturnProblem()
    {
        // Arrange
        var symbol = "AAPL";
        var brokerId = "finnhub";
        var from = DateTime.UtcNow.AddDays(-30);
        var to = DateTime.UtcNow;
        
        var mockProvider = new MockHistoricalPriceProvider 
        { 
            BrokerId = brokerId, 
            DataToReturn = null // Simulate failure
        };
        
        SetupUserClaims(brokerId);
        _mockHistoryRegistry.Setup(r => r.GetProvider(brokerId))
                          .Returns(mockProvider);

        // Act
        var result = await _controller.History(symbol, from, to);

        // Assert
        result.Should().BeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.Should().Be(500);
    }
}
