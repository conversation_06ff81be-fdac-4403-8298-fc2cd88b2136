using FluentAssertions;
using MarketDataService.Controllers;
using MarketDataService.Interfaces;
using MarketDataService.Models;
using MarketDataService.Tests.TestHelpers;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace MarketDataService.Tests.Controllers;

public class BrokersControllerTests
{
    private readonly Mock<IBrokerMetadataRegistry> _mockRegistry;
    private readonly BrokersController _controller;

    public BrokersControllerTests()
    {
        _mockRegistry = new Mock<IBrokerMetadataRegistry>();
        _controller = new BrokersController(_mockRegistry.Object);
    }

    [Fact]
    public void Get_WithAvailableBrokers_ShouldReturnOkWithBrokerList()
    {
        // Arrange
        var brokerMetadata = new List<BrokerMetadata>
        {
            TestDataBuilder.CreateBrokerMetadata("finnhub", "Finnhub", "online"),
            TestDataBuilder.CreateBrokerMetadata("polygon", "Polygon.io", "online"),
            TestDataBuilder.CreateBrokerMetadata("alpha-vantage", "Alpha Vantage", "offline")
        };

        _mockRegistry.Setup(r => r.ListAll())
                    .Returns(brokerMetadata);

        // Act
        var result = _controller.Get();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var returnedBrokers = okResult.Value as IEnumerable<BrokerMetadata>;
        
        returnedBrokers.Should().NotBeNull();
        returnedBrokers.Should().HaveCount(3);
        returnedBrokers.Should().Contain(b => b.Id == "finnhub" && b.Name == "Finnhub");
        returnedBrokers.Should().Contain(b => b.Id == "polygon" && b.Name == "Polygon.io");
        returnedBrokers.Should().Contain(b => b.Id == "alpha-vantage" && b.Status == "offline");
    }

    [Fact]
    public void Get_WithNoBrokers_ShouldReturnOkWithEmptyList()
    {
        // Arrange
        _mockRegistry.Setup(r => r.ListAll())
                    .Returns(new List<BrokerMetadata>());

        // Act
        var result = _controller.Get();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var returnedBrokers = okResult.Value as IEnumerable<BrokerMetadata>;
        
        returnedBrokers.Should().NotBeNull();
        returnedBrokers.Should().BeEmpty();
    }

    [Fact]
    public void Get_ShouldCallRegistryListAll()
    {
        // Arrange
        _mockRegistry.Setup(r => r.ListAll())
                    .Returns(new List<BrokerMetadata>());

        // Act
        _controller.Get();

        // Assert
        _mockRegistry.Verify(r => r.ListAll(), Times.Once);
    }
}
