using FluentAssertions;
using MarketDataService.Controllers;
using MarketDataService.Interfaces;
using MarketDataService.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace MarketDataService.Tests.Controllers;

public class SymbolControllerTests
{
    private readonly Mock<ISymbolRegistry> _mockSymbolRegistry;
    private readonly Mock<ILogger<SymbolController>> _mockLogger;
    private readonly SymbolController _controller;

    public SymbolControllerTests()
    {
        _mockSymbolRegistry = new Mock<ISymbolRegistry>();
        _mockLogger = new Mock<ILogger<SymbolController>>();
        _controller = new SymbolController(_mockSymbolRegistry.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GetAllSymbols_ShouldReturnOkWithSymbols()
    {
        // Arrange
        var symbolResponse = new SymbolResponse<Symbol>
        {
            Symbols = new List<Symbol>
            {
                new() { Code = "AAPL", Name = "Apple Inc.", MarketType = "stocks" }
            },
            TotalCount = 1,
            BrokerId = "all"
        };

        _mockSymbolRegistry.Setup(r => r.GetAllSymbolsAsync(null))
            .ReturnsAsync(symbolResponse);

        // Act
        var result = await _controller.GetAllSymbols();

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().Be(symbolResponse);
    }

    [Fact]
    public async Task GetSymbolsByMarketType_WithValidMarketType_ShouldReturnOkWithSymbols()
    {
        // Arrange
        var symbolResponse = new SymbolResponse<Symbol>
        {
            Symbols = new List<Symbol>
            {
                new() { Code = "AAPL", Name = "Apple Inc.", MarketType = "stocks" }
            },
            TotalCount = 1,
            BrokerId = "all"
        };

        _mockSymbolRegistry.Setup(r => r.GetSymbolsByMarketTypeAsync("stocks", null))
            .ReturnsAsync(symbolResponse);

        // Act
        var result = await _controller.GetSymbolsByMarketType("stocks");

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().Be(symbolResponse);
    }

    [Fact]
    public async Task GetSymbolsByMarketType_WithInvalidMarketType_ShouldReturnBadRequest()
    {
        // Act
        var result = await _controller.GetSymbolsByMarketType("invalid");

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().Be("Invalid market type. Valid types: stocks, forex, crypto");
    }

    [Fact]
    public async Task GetSymbolsByMarketType_WithEmptyMarketType_ShouldReturnBadRequest()
    {
        // Act
        var result = await _controller.GetSymbolsByMarketType("");

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().Be("Market type is required");
    }

    [Fact]
    public async Task GetMostActiveSymbols_ShouldReturnOkWithActiveSymbols()
    {
        // Arrange
        var activeSymbolResponse = new SymbolResponse<ActiveSymbol>
        {
            Symbols = new List<ActiveSymbol>
            {
                new() { Code = "AAPL", Name = "Apple Inc.", MarketType = "stocks", Rank = 1, Volume = 1000 }
            },
            TotalCount = 1,
            BrokerId = "all"
        };

        _mockSymbolRegistry.Setup(r => r.GetMostActiveSymbolsAsync(null, null, 50))
            .ReturnsAsync(activeSymbolResponse);

        // Act
        var result = await _controller.GetMostActiveSymbols();

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().Be(activeSymbolResponse);
    }

    [Fact]
    public async Task GetMostActiveSymbols_WithInvalidLimit_ShouldReturnBadRequest()
    {
        // Act
        var result = await _controller.GetMostActiveSymbols(limit: 0);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().Be("Limit must be between 1 and 100");
    }

    [Fact]
    public async Task GetMostActiveSymbols_WithLimitTooHigh_ShouldReturnBadRequest()
    {
        // Act
        var result = await _controller.GetMostActiveSymbols(limit: 101);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().Be("Limit must be between 1 and 100");
    }

    [Fact]
    public async Task SearchSymbols_WithValidQuery_ShouldReturnOkWithResults()
    {
        // Arrange
        var searchResponse = new SymbolResponse<SymbolSearchResult>
        {
            Symbols = new List<SymbolSearchResult>
            {
                new() { Code = "AAPL", Name = "Apple Inc.", MarketType = "stocks", RelevanceScore = 100 }
            },
            TotalCount = 1,
            BrokerId = "all"
        };

        _mockSymbolRegistry.Setup(r => r.SearchSymbolsAsync("apple", null, null, 20))
            .ReturnsAsync(searchResponse);

        // Act
        var result = await _controller.SearchSymbols("apple");

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().Be(searchResponse);
    }

    [Fact]
    public async Task SearchSymbols_WithEmptyQuery_ShouldReturnBadRequest()
    {
        // Act
        var result = await _controller.SearchSymbols("");

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().Be("Search query is required");
    }

    [Fact]
    public async Task SearchSymbols_WithInvalidLimit_ShouldReturnBadRequest()
    {
        // Act
        var result = await _controller.SearchSymbols("apple", limit: 0);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().Be("Limit must be between 1 and 100");
    }

    [Fact]
    public void GetAvailableBrokers_ShouldReturnOkWithBrokerIds()
    {
        // Arrange
        var brokerIds = new[] { "broker1", "broker2" };
        _mockSymbolRegistry.Setup(r => r.GetAvailableBrokerIds())
            .Returns(brokerIds);

        // Act
        var result = _controller.GetAvailableBrokers();

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().Be(brokerIds);
    }

    [Fact]
    public async Task BulkSearchSymbols_WithValidRequest_ShouldReturnOkWithResults()
    {
        // Arrange
        var request = new SymbolSearchRequest
        {
            Query = "apple",
            MarketType = "stocks",
            BrokerId = "broker1",
            Limit = 10
        };

        var searchResponse = new SymbolResponse<SymbolSearchResult>
        {
            Symbols = new List<SymbolSearchResult>
            {
                new() { Code = "AAPL", Name = "Apple Inc.", MarketType = "stocks", RelevanceScore = 100 }
            },
            TotalCount = 1,
            BrokerId = "broker1"
        };

        _mockSymbolRegistry.Setup(r => r.SearchSymbolsAsync("apple", "stocks", "broker1", 10))
            .ReturnsAsync(searchResponse);

        // Act
        var result = await _controller.BulkSearchSymbols(request);

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().Be(searchResponse);
    }

    [Fact]
    public async Task BulkSearchSymbols_WithEmptyQuery_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new SymbolSearchRequest { Query = "", Limit = 10 };

        // Act
        var result = await _controller.BulkSearchSymbols(request);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().Be("Search query is required");
    }

    [Fact]
    public async Task BulkGetMostActiveSymbols_WithValidRequest_ShouldReturnOkWithResults()
    {
        // Arrange
        var request = new MostActiveSymbolsRequest
        {
            MarketType = "stocks",
            BrokerId = "broker1",
            Limit = 25
        };

        var activeResponse = new SymbolResponse<ActiveSymbol>
        {
            Symbols = new List<ActiveSymbol>
            {
                new() { Code = "AAPL", Name = "Apple Inc.", MarketType = "stocks", Rank = 1, Volume = 1000 }
            },
            TotalCount = 1,
            BrokerId = "broker1"
        };

        _mockSymbolRegistry.Setup(r => r.GetMostActiveSymbolsAsync("stocks", "broker1", 25))
            .ReturnsAsync(activeResponse);

        // Act
        var result = await _controller.BulkGetMostActiveSymbols(request);

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().Be(activeResponse);
    }

    [Fact]
    public async Task BulkGetMostActiveSymbols_WithInvalidLimit_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new MostActiveSymbolsRequest { Limit = 0 };

        // Act
        var result = await _controller.BulkGetMostActiveSymbols(request);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().Be("Limit must be between 1 and 100");
    }
}
