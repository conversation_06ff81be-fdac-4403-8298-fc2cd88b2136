using FluentAssertions;
using MarketDataService.Configuration;
using MarketDataService.Data;
using MarketDataService.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Moq;
using StackExchange.Redis;
using System.Net;
using System.Text.Json;
using Xunit;

namespace MarketDataService.Tests.Integration;

public class ApplicationStartupTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public ApplicationStartupTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Replace the database with in-memory database
                services.RemoveAll(typeof(DbContextOptions<MarketDataContext>));
                services.AddDbContext<MarketDataContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb");
                });

                // Mock Redis
                var mockRedis = new Mock<IConnectionMultiplexer>();
                var mockDatabase = new Mock<IDatabase>();
                mockRedis.Setup(r => r.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
                        .Returns(mockDatabase.Object);
                
                services.RemoveAll(typeof(IConnectionMultiplexer));
                services.AddSingleton(mockRedis.Object);

                // Configure test settings by setting environment variables
                Environment.SetEnvironmentVariable("FINNHUB_API_KEY", "test-finnhub-key");
                Environment.SetEnvironmentVariable("POLYGON_API_KEY", "test-polygon-key");
                Environment.SetEnvironmentVariable("SUPABASE_URL", "https://test.supabase.co");
                Environment.SetEnvironmentVariable("SUPABASE_JWT_SECRET", "test-jwt-secret-key-that-is-at-least-32-characters-long");
            });
        });

        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetBrokers_ShouldReturnAvailableBrokers()
    {
        // Act
        var response = await _client.GetAsync("/brokers");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var brokers = JsonSerializer.Deserialize<BrokerMetadata[]>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        brokers.Should().NotBeNull();
        brokers.Should().NotBeEmpty();
        brokers.Should().Contain(b => b.Id == "finnhub");
    }

    [Fact]
    public async Task Application_ShouldStartSuccessfully()
    {
        // This test verifies that the application can start without errors
        // Act
        var response = await _client.GetAsync("/brokers");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }
}
