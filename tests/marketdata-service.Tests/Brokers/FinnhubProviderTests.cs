using FluentAssertions;
using MarketDataService.Brokers;
using MarketDataService.Configuration;
using MarketDataService.Tests.TestHelpers;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using System.Net;
using System.Text.Json;
using Xunit;

namespace MarketDataService.Tests.Brokers;

public class FinnhubProviderTests
{
    private readonly Mock<HttpMessageHandler> _mockHttpHandler;
    private readonly HttpClient _httpClient;
    private readonly Mock<IOptions<BrokerApiKeys>> _mockOptions;
    private readonly FinnhubProvider _provider;

    public FinnhubProviderTests()
    {
        _mockHttpHandler = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_mockHttpHandler.Object);
        
        _mockOptions = new Mock<IOptions<BrokerApiKeys>>();
        var testApiKeys = new TestBrokerApiKeys { Finnhub = "test-api-key" };
        _mockOptions.Setup(o => o.Value).Returns(testApiKeys);
        
        _provider = new FinnhubProvider(_httpClient, _mockOptions.Object);
    }

    [Fact]
    public void BrokerId_ShouldReturnFinnhub()
    {
        // Act & Assert
        _provider.BrokerId.Should().Be("finnhub");
    }

    [Fact]
    public async Task FetchPriceAsync_WithSuccessfulResponse_ShouldReturnPrice()
    {
        // Arrange
        var symbol = "AAPL";
        var expectedPrice = 150.25m;
        var responseJson = JsonSerializer.Serialize(new { c = expectedPrice });
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson)
            });

        // Act
        var result = await _provider.FetchPriceAsync(symbol);

        // Assert
        result.Should().Be(expectedPrice);
    }

    [Fact]
    public async Task FetchPriceAsync_WithFailedResponse_ShouldReturnNull()
    {
        // Arrange
        var symbol = "INVALID";
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            });

        // Act
        var result = await _provider.FetchPriceAsync(symbol);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task FetchPriceAsync_WithInvalidJson_ShouldReturnNull()
    {
        // Arrange
        var symbol = "AAPL";
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("invalid json")
            });

        // Act
        var result = await _provider.FetchPriceAsync(symbol);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetHistoricalPricesAsync_WithSuccessfulResponse_ShouldReturnHistoricalData()
    {
        // Arrange
        var symbol = "AAPL";
        var from = DateTime.UtcNow.AddDays(-2);
        var to = DateTime.UtcNow;
        var interval = "1d";
        
        var responseData = new
        {
            s = "ok",
            t = new long[] { **********, ********** }, // Unix timestamps
            o = new decimal[] { 100m, 105m },
            h = new decimal[] { 110m, 115m },
            l = new decimal[] { 95m, 100m },
            c = new decimal[] { 108m, 112m },
            v = new long[] { 1000000, 1200000 }
        };
        var responseJson = JsonSerializer.Serialize(responseData);
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req => 
                    req.RequestUri!.ToString().Contains("stock/candle")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson)
            });

        // Act
        var result = await _provider.GetHistoricalPricesAsync(symbol, from, to, interval);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result![0].Open.Should().Be(100m);
        result[0].High.Should().Be(110m);
        result[0].Low.Should().Be(95m);
        result[0].Close.Should().Be(108m);
        result[0].Volume.Should().Be(1000000);
    }

    [Fact]
    public async Task GetHistoricalPricesAsync_WithFailedResponse_ShouldReturnNull()
    {
        // Arrange
        var symbol = "INVALID";
        var from = DateTime.UtcNow.AddDays(-2);
        var to = DateTime.UtcNow;
        var interval = "1d";
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            });

        // Act
        var result = await _provider.GetHistoricalPricesAsync(symbol, from, to, interval);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void GetMetadata_ShouldReturnCorrectMetadata()
    {
        // Act
        var result = _provider.GetMetadata();

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be("finnhub");
        result.Name.Should().Be("Finnhub");
        result.Status.Should().Be("online");
        result.SupportedAssets.Should().Contain("stocks");
        result.SupportedAssets.Should().Contain("crypto");
        result.SupportedAssets.Should().Contain("forex");
        result.DocsUrl.Should().Be("https://finnhub.io/docs/api");
    }

    // Symbol Provider Tests
    [Fact]
    public async Task GetSymbolsByMarketTypeAsync_WithStocks_ShouldReturnStockSymbols()
    {
        // Arrange
        var stockSymbolsJson = """
        [
            {"symbol": "AAPL", "description": "Apple Inc", "currency": "USD"},
            {"symbol": "GOOGL", "description": "Alphabet Inc", "currency": "USD"}
        ]
        """;

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req => req.RequestUri!.ToString().Contains("/stock/symbol")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(stockSymbolsJson)
            });

        // Act
        var result = await _provider.GetSymbolsByMarketTypeAsync("stocks");

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().Contain(s => s.Code == "AAPL" && s.Name == "Apple Inc");
        result.Should().Contain(s => s.Code == "GOOGL" && s.Name == "Alphabet Inc");
        result.Should().OnlyContain(s => s.MarketType == "stocks");
        result.Should().OnlyContain(s => s.BrokerId == "finnhub");
    }

    [Fact]
    public async Task GetMostActiveSymbolsAsync_ShouldReturnPopularStocks()
    {
        // Act
        var result = await _provider.GetMostActiveSymbolsAsync("stocks", 5);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(5);
        result.Should().OnlyContain(s => s.MarketType == "stocks");
        result.Should().OnlyContain(s => s.BrokerId == "finnhub");
        result.Should().Contain(s => s.Code == "AAPL");
        result.Should().BeInAscendingOrder(s => s.Rank);
    }

    [Fact]
    public async Task SearchSymbolsAsync_WithValidQuery_ShouldReturnSearchResults()
    {
        // Arrange
        var searchResultJson = """
        {
            "result": [
                {"symbol": "AAPL", "description": "Apple Inc", "type": "Common Stock"},
                {"symbol": "AAPLW", "description": "Apple Inc Warrant", "type": "Warrant"}
            ]
        }
        """;

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req => req.RequestUri!.ToString().Contains("/search")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(searchResultJson)
            });

        // Act
        var result = await _provider.SearchSymbolsAsync("apple");

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().Contain(s => s.Code == "AAPL" && s.Name == "Apple Inc");
        result.Should().OnlyContain(s => s.BrokerId == "finnhub");
        result.Should().BeInDescendingOrder(s => s.RelevanceScore);
    }

    [Fact]
    public async Task GetAllSymbolsAsync_ShouldReturnSymbolsFromAllMarketTypes()
    {
        // Arrange - Mock responses for all market types
        var stockSymbolsJson = """[{"symbol": "AAPL", "description": "Apple Inc", "currency": "USD"}]""";
        var forexSymbolsJson = """[{"symbol": "EUR/USD", "description": "Euro US Dollar"}]""";
        var cryptoSymbolsJson = """[{"symbol": "BTCUSD", "description": "Bitcoin USD"}]""";

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req => req.RequestUri!.ToString().Contains("/stock/symbol")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(stockSymbolsJson)
            });

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req => req.RequestUri!.ToString().Contains("/forex/symbol")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(forexSymbolsJson)
            });

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req => req.RequestUri!.ToString().Contains("/crypto/symbol")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(cryptoSymbolsJson)
            });

        // Act
        var result = await _provider.GetAllSymbolsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(3);
        result.Should().Contain(s => s.Code == "AAPL" && s.MarketType == "stocks");
        result.Should().Contain(s => s.Code == "EUR/USD" && s.MarketType == "forex");
        result.Should().Contain(s => s.Code == "BTCUSD" && s.MarketType == "crypto");
        result.Should().OnlyContain(s => s.BrokerId == "finnhub");
    }
}
