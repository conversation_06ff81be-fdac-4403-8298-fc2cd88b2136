using FluentAssertions;
using MarketDataService.Brokers;
using MarketDataService.Configuration;
using MarketDataService.Tests.TestHelpers;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using System.Net;
using System.Text.Json;
using Xunit;

namespace MarketDataService.Tests.Brokers;

public class PolygonProviderTests
{
    private readonly Mock<HttpMessageHandler> _mockHttpHandler;
    private readonly HttpClient _httpClient;
    private readonly Mock<IOptions<BrokerApiKeys>> _mockOptions;
    private readonly PolygonProvider _provider;

    public PolygonProviderTests()
    {
        _mockHttpHandler = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_mockHttpHandler.Object);
        
        _mockOptions = new Mock<IOptions<BrokerApiKeys>>();
        var testApiKeys = new TestBrokerApiKeys { Polygon = "test-api-key" };
        _mockOptions.Setup(o => o.Value).Returns(testApiKeys);
        
        _provider = new PolygonProvider(_httpClient, _mockOptions.Object);
    }

    [Fact]
    public void BrokerId_ShouldReturnPolygon()
    {
        // Act & Assert
        _provider.BrokerId.Should().Be("polygon");
    }

    [Fact]
    public async Task FetchPriceAsync_WithSuccessfulResponse_ShouldReturnPrice()
    {
        // Arrange
        var symbol = "AAPL";
        var expectedPrice = 155.75m;
        var responseData = new
        {
            Last = new { p = expectedPrice }
        };
        var responseJson = JsonSerializer.Serialize(responseData);
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson)
            });

        // Act
        var result = await _provider.FetchPriceAsync(symbol);

        // Assert
        result.Should().Be(expectedPrice);
    }

    [Fact]
    public async Task FetchPriceAsync_WithFailedResponse_ShouldReturnNull()
    {
        // Arrange
        var symbol = "INVALID";
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            });

        // Act
        var result = await _provider.FetchPriceAsync(symbol);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task FetchPriceAsync_WithNullLastTrade_ShouldReturnNull()
    {
        // Arrange
        var symbol = "AAPL";
        var responseData = new { Last = (object?)null };
        var responseJson = JsonSerializer.Serialize(responseData);
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson)
            });

        // Act
        var result = await _provider.FetchPriceAsync(symbol);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetHistoricalPricesAsync_WithSuccessfulResponse_ShouldReturnHistoricalData()
    {
        // Arrange
        var symbol = "AAPL";
        var from = DateTime.UtcNow.AddDays(-2);
        var to = DateTime.UtcNow;
        var interval = "1d";
        
        var responseData = new
        {
            Results = new[]
            {
                new { t = 1640995200000L, o = 100m, h = 110m, l = 95m, c = 108m, v = 1000000L },
                new { t = 1641081600000L, o = 105m, h = 115m, l = 100m, c = 112m, v = 1200000L }
            }
        };
        var responseJson = JsonSerializer.Serialize(responseData);
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req => 
                    req.RequestUri!.ToString().Contains("aggs/ticker")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson)
            });

        // Act
        var result = await _provider.GetHistoricalPricesAsync(symbol, from, to, interval);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result![0].Open.Should().Be(100m);
        result[0].High.Should().Be(110m);
        result[0].Low.Should().Be(95m);
        result[0].Close.Should().Be(108m);
        result[0].Volume.Should().Be(1000000);
    }

    [Fact]
    public async Task GetHistoricalPricesAsync_WithFailedResponse_ShouldReturnNull()
    {
        // Arrange
        var symbol = "INVALID";
        var from = DateTime.UtcNow.AddDays(-2);
        var to = DateTime.UtcNow;
        var interval = "1d";
        
        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            });

        // Act
        var result = await _provider.GetHistoricalPricesAsync(symbol, from, to, interval);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void GetMetadata_ShouldReturnCorrectMetadata()
    {
        // Act
        var result = _provider.GetMetadata();

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be("polygon");
        result.Name.Should().Be("Polygon.io");
        result.Status.Should().Be("online");
        result.SupportedAssets.Should().Contain("stocks");
        result.SupportedAssets.Should().Contain("crypto");
        result.DocsUrl.Should().Be("https://polygon.io/docs");
    }
}
