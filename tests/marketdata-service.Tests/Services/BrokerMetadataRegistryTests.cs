using FluentAssertions;
using MarketDataService.Interfaces;
using MarketDataService.Services;
using MarketDataService.Tests.TestHelpers;
using Xunit;

namespace MarketDataService.Tests.Services;

public class BrokerMetadataRegistryTests
{
    [Fact]
    public void Constructor_WithProviders_ShouldInitializeCorrectly()
    {
        // Arrange
        var providers = new[]
        {
            new MockBrokerMetadataProvider { BrokerId = "broker1" },
            new MockBrokerMetadataProvider { BrokerId = "broker2" }
        };

        // Act
        var registry = new BrokerMetadataRegistry(providers);

        // Assert
        registry.Should().NotBeNull();
    }

    [Fact]
    public void ListAll_WithProviders_ShouldReturnAllMetadata()
    {
        // Arrange
        var metadata1 = TestDataBuilder.CreateBrokerMetadata("broker1", "Broker One");
        var metadata2 = TestDataBuilder.CreateBrokerMetadata("broker2", "Broker Two");
        
        var providers = new[]
        {
            new MockBrokerMetadataProvider { BrokerId = "broker1", MetadataToReturn = metadata1 },
            new MockBrokerMetadataProvider { BrokerId = "broker2", MetadataToReturn = metadata2 }
        };
        var registry = new BrokerMetadataRegistry(providers);

        // Act
        var result = registry.ListAll().ToList();

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(m => m.Id == "broker1" && m.Name == "Broker One");
        result.Should().Contain(m => m.Id == "broker2" && m.Name == "Broker Two");
    }

    [Fact]
    public void ListAll_WithEmptyProviders_ShouldReturnEmptyList()
    {
        // Arrange
        var registry = new BrokerMetadataRegistry(Array.Empty<MockBrokerMetadataProvider>());

        // Act
        var result = registry.ListAll();

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void ListAll_ShouldCallGetMetadataOnAllProviders()
    {
        // Arrange
        var provider1 = new MockBrokerMetadataProvider { BrokerId = "broker1" };
        var provider2 = new MockBrokerMetadataProvider { BrokerId = "broker2" };
        var registry = new BrokerMetadataRegistry(new[] { provider1, provider2 });

        // Act
        var result = registry.ListAll().ToList();

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(m => m != null);
    }
}
