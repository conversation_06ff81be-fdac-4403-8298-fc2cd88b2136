using FluentAssertions;
using MarketDataService.Interfaces;
using MarketDataService.Services;
using MarketDataService.Tests.TestHelpers;
using Moq;
using StackExchange.Redis;
using System.Text.Json;
using Xunit;

namespace MarketDataService.Tests.Services;

public class RedisCacheServiceTests
{
    private readonly Mock<IConnectionMultiplexer> _mockRedis;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly RedisCacheService _cacheService;

    public RedisCacheServiceTests()
    {
        _mockRedis = new Mock<IConnectionMultiplexer>();
        _mockDatabase = new Mock<IDatabase>();
        _mockRedis.Setup(r => r.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
                  .Returns(_mockDatabase.Object);
        
        _cacheService = new RedisCacheService(_mockRedis.Object);
    }

    [Fact]
    public async Task GetAsync_WithExistingKey_ShouldReturnDeserializedValue()
    {
        // Arrange
        var testData = TestDataBuilder.CreateMarketData("AAPL", 150.00m);
        var serializedData = JsonSerializer.Serialize(testData);
        
        _mockDatabase.Setup(db => db.StringGetAsync("test-key", It.IsAny<CommandFlags>()))
                    .ReturnsAsync(new RedisValue(serializedData));

        // Act
        var result = await _cacheService.GetAsync<MarketDataService.Models.MarketData>("test-key");

        // Assert
        result.Should().NotBeNull();
        result!.Symbol.Should().Be("AAPL");
        result.Price.Should().Be(150.00m);
    }

    [Fact]
    public async Task GetAsync_WithNonExistentKey_ShouldReturnNull()
    {
        // Arrange
        _mockDatabase.Setup(db => db.StringGetAsync("nonexistent-key", It.IsAny<CommandFlags>()))
                    .ReturnsAsync(RedisValue.Null);

        // Act
        var result = await _cacheService.GetAsync<MarketDataService.Models.MarketData>("nonexistent-key");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task SetAsync_WithValue_ShouldSerializeAndStore()
    {
        // Arrange
        var testData = TestDataBuilder.CreateMarketData("AAPL", 150.00m);
        var expiry = TimeSpan.FromMinutes(5);

        // Act
        await _cacheService.SetAsync("test-key", testData, expiry);

        // Assert
        _mockDatabase.Verify(db => db.StringSetAsync(
            "test-key",
            It.IsAny<RedisValue>(),
            expiry,
            false,
            When.Always,
            CommandFlags.None), Times.Once);
    }

    [Fact]
    public async Task SetAsync_WithoutExpiry_ShouldStoreWithoutExpiration()
    {
        // Arrange
        var testData = TestDataBuilder.CreateMarketData("AAPL", 150.00m);

        // Act
        await _cacheService.SetAsync("test-key", testData);

        // Assert
        _mockDatabase.Verify(db => db.StringSetAsync(
            "test-key",
            It.IsAny<RedisValue>(),
            null,
            false,
            When.Always,
            CommandFlags.None), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_WithExistingKey_ShouldReturnTrue()
    {
        // Arrange
        _mockDatabase.Setup(db => db.KeyDeleteAsync("test-key", It.IsAny<CommandFlags>()))
                    .ReturnsAsync(true);

        // Act
        var result = await _cacheService.DeleteAsync("test-key");

        // Assert
        result.Should().BeTrue();
        _mockDatabase.Verify(db => db.KeyDeleteAsync("test-key", It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_WithNonExistentKey_ShouldReturnFalse()
    {
        // Arrange
        _mockDatabase.Setup(db => db.KeyDeleteAsync("nonexistent-key", It.IsAny<CommandFlags>()))
                    .ReturnsAsync(false);

        // Act
        var result = await _cacheService.DeleteAsync("nonexistent-key");

        // Assert
        result.Should().BeFalse();
    }
}
