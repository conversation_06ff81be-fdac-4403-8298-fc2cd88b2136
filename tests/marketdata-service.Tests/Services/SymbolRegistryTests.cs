using FluentAssertions;
using MarketDataService.Interfaces;
using MarketDataService.Models;
using MarketDataService.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace MarketDataService.Tests.Services;

public class SymbolRegistryTests
{
    private readonly Mock<ILogger<SymbolRegistry>> _mockLogger;
    private readonly Mock<ISymbolProvider> _mockProvider1;
    private readonly Mock<ISymbolProvider> _mockProvider2;
    private readonly SymbolRegistry _registry;

    public SymbolRegistryTests()
    {
        _mockLogger = new Mock<ILogger<SymbolRegistry>>();
        _mockProvider1 = new Mock<ISymbolProvider>();
        _mockProvider2 = new Mock<ISymbolProvider>();

        _mockProvider1.Setup(p => p.BrokerId).Returns("broker1");
        _mockProvider2.Setup(p => p.BrokerId).Returns("broker2");

        var providers = new[] { _mockProvider1.Object, _mockProvider2.Object };
        _registry = new SymbolRegistry(providers, _mockLogger.Object);
    }

    [Fact]
    public void GetAllProviders_ShouldReturnAllProviders()
    {
        // Act
        var result = _registry.GetAllProviders();

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(_mockProvider1.Object);
        result.Should().Contain(_mockProvider2.Object);
    }

    [Fact]
    public void GetProvider_WithValidBrokerId_ShouldReturnProvider()
    {
        // Act
        var result = _registry.GetProvider("broker1");

        // Assert
        result.Should().Be(_mockProvider1.Object);
    }

    [Fact]
    public void GetProvider_WithInvalidBrokerId_ShouldReturnNull()
    {
        // Act
        var result = _registry.GetProvider("invalid");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void GetAvailableBrokerIds_ShouldReturnAllBrokerIds()
    {
        // Act
        var result = _registry.GetAvailableBrokerIds();

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain("broker1");
        result.Should().Contain("broker2");
    }

    [Fact]
    public async Task GetAllSymbolsAsync_WithoutBrokerId_ShouldReturnSymbolsFromAllProviders()
    {
        // Arrange
        var symbols1 = new List<Symbol>
        {
            new() { Code = "AAPL", Name = "Apple Inc.", BrokerId = "broker1", MarketType = "stocks" }
        };
        var symbols2 = new List<Symbol>
        {
            new() { Code = "GOOGL", Name = "Alphabet Inc.", BrokerId = "broker2", MarketType = "stocks" }
        };

        _mockProvider1.Setup(p => p.GetAllSymbolsAsync()).ReturnsAsync(symbols1);
        _mockProvider2.Setup(p => p.GetAllSymbolsAsync()).ReturnsAsync(symbols2);

        // Act
        var result = await _registry.GetAllSymbolsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Symbols.Should().HaveCount(2);
        result.Symbols.Should().Contain(s => s.Code == "AAPL");
        result.Symbols.Should().Contain(s => s.Code == "GOOGL");
        result.BrokerId.Should().Be("all");
        result.TotalCount.Should().Be(2);
    }

    [Fact]
    public async Task GetAllSymbolsAsync_WithSpecificBrokerId_ShouldReturnSymbolsFromSpecificProvider()
    {
        // Arrange
        var symbols1 = new List<Symbol>
        {
            new() { Code = "AAPL", Name = "Apple Inc.", BrokerId = "broker1", MarketType = "stocks" }
        };

        _mockProvider1.Setup(p => p.GetAllSymbolsAsync()).ReturnsAsync(symbols1);

        // Act
        var result = await _registry.GetAllSymbolsAsync("broker1");

        // Assert
        result.Should().NotBeNull();
        result.Symbols.Should().HaveCount(1);
        result.Symbols.Should().Contain(s => s.Code == "AAPL");
        result.BrokerId.Should().Be("broker1");
        result.TotalCount.Should().Be(1);

        // Verify only the specific provider was called
        _mockProvider1.Verify(p => p.GetAllSymbolsAsync(), Times.Once);
        _mockProvider2.Verify(p => p.GetAllSymbolsAsync(), Times.Never);
    }

    [Fact]
    public async Task GetSymbolsByMarketTypeAsync_ShouldReturnFilteredSymbols()
    {
        // Arrange
        var stockSymbols1 = new List<Symbol>
        {
            new() { Code = "AAPL", Name = "Apple Inc.", BrokerId = "broker1", MarketType = "stocks" }
        };
        var stockSymbols2 = new List<Symbol>
        {
            new() { Code = "GOOGL", Name = "Alphabet Inc.", BrokerId = "broker2", MarketType = "stocks" }
        };

        _mockProvider1.Setup(p => p.GetSymbolsByMarketTypeAsync("stocks")).ReturnsAsync(stockSymbols1);
        _mockProvider2.Setup(p => p.GetSymbolsByMarketTypeAsync("stocks")).ReturnsAsync(stockSymbols2);

        // Act
        var result = await _registry.GetSymbolsByMarketTypeAsync("stocks");

        // Assert
        result.Should().NotBeNull();
        result.Symbols.Should().HaveCount(2);
        result.Symbols.Should().OnlyContain(s => s.MarketType == "stocks");
        result.BrokerId.Should().Be("all");
        result.TotalCount.Should().Be(2);
    }

    [Fact]
    public async Task GetMostActiveSymbolsAsync_ShouldReturnSortedActiveSymbols()
    {
        // Arrange
        var activeSymbols1 = new List<ActiveSymbol>
        {
            new() { Code = "AAPL", Name = "Apple Inc.", BrokerId = "broker1", Rank = 2, Volume = 1000 }
        };
        var activeSymbols2 = new List<ActiveSymbol>
        {
            new() { Code = "GOOGL", Name = "Alphabet Inc.", BrokerId = "broker2", Rank = 1, Volume = 2000 }
        };

        _mockProvider1.Setup(p => p.GetMostActiveSymbolsAsync(null, 50)).ReturnsAsync(activeSymbols1);
        _mockProvider2.Setup(p => p.GetMostActiveSymbolsAsync(null, 50)).ReturnsAsync(activeSymbols2);

        // Act
        var result = await _registry.GetMostActiveSymbolsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Symbols.Should().HaveCount(2);
        result.Symbols.First().Code.Should().Be("GOOGL"); // Should be sorted by rank
        result.BrokerId.Should().Be("all");
        result.TotalCount.Should().Be(2);
    }

    [Fact]
    public async Task SearchSymbolsAsync_ShouldReturnSortedSearchResults()
    {
        // Arrange
        var searchResults1 = new List<SymbolSearchResult>
        {
            new() { Code = "AAPL", Name = "Apple Inc.", BrokerId = "broker1", RelevanceScore = 80 }
        };
        var searchResults2 = new List<SymbolSearchResult>
        {
            new() { Code = "GOOGL", Name = "Alphabet Inc.", BrokerId = "broker2", RelevanceScore = 90 }
        };

        _mockProvider1.Setup(p => p.SearchSymbolsAsync("apple", null, 20)).ReturnsAsync(searchResults1);
        _mockProvider2.Setup(p => p.SearchSymbolsAsync("apple", null, 20)).ReturnsAsync(searchResults2);

        // Act
        var result = await _registry.SearchSymbolsAsync("apple");

        // Assert
        result.Should().NotBeNull();
        result.Symbols.Should().HaveCount(2);
        result.Symbols.First().Code.Should().Be("GOOGL"); // Should be sorted by relevance score
        result.BrokerId.Should().Be("all");
        result.TotalCount.Should().Be(2);
    }

    [Fact]
    public async Task GetAllSymbolsAsync_WhenProviderThrows_ShouldContinueWithOtherProviders()
    {
        // Arrange
        var symbols2 = new List<Symbol>
        {
            new() { Code = "GOOGL", Name = "Alphabet Inc.", BrokerId = "broker2", MarketType = "stocks" }
        };

        _mockProvider1.Setup(p => p.GetAllSymbolsAsync()).ThrowsAsync(new Exception("Provider error"));
        _mockProvider2.Setup(p => p.GetAllSymbolsAsync()).ReturnsAsync(symbols2);

        // Act
        var result = await _registry.GetAllSymbolsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Symbols.Should().HaveCount(1);
        result.Symbols.Should().Contain(s => s.Code == "GOOGL");
        result.TotalCount.Should().Be(1);
    }
}
