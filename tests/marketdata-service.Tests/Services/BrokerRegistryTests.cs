using FluentAssertions;
using MarketDataService.Interfaces;
using MarketDataService.Services;
using MarketDataService.Tests.TestHelpers;
using Xunit;

namespace MarketDataService.Tests.Services;

public class BrokerRegistryTests
{
    [Fact]
    public void Constructor_WithProviders_ShouldInitializeCorrectly()
    {
        // Arrange
        var providers = new[]
        {
            new MockPriceProvider { BrokerId = "broker1" },
            new MockPriceProvider { BrokerId = "broker2" }
        };

        // Act
        var registry = new BrokerRegistry(providers);

        // Assert
        registry.Should().NotBeNull();
        registry.GetAllProviders().Should().HaveCount(2);
    }

    [Fact]
    public void GetProvider_WithValidBrokerId_ShouldReturnProvider()
    {
        // Arrange
        var provider1 = new MockPriceProvider { BrokerId = "broker1" };
        var provider2 = new MockPriceProvider { BrokerId = "broker2" };
        var registry = new BrokerRegistry(new[] { provider1, provider2 });

        // Act
        var result = registry.GetProvider("broker1");

        // Assert
        result.Should().NotBeNull();
        result!.BrokerId.Should().Be("broker1");
        result.Should().BeSameAs(provider1);
    }

    [Fact]
    public void GetProvider_WithInvalidBrokerId_ShouldReturnNull()
    {
        // Arrange
        var providers = new[]
        {
            new MockPriceProvider { BrokerId = "broker1" },
            new MockPriceProvider { BrokerId = "broker2" }
        };
        var registry = new BrokerRegistry(providers);

        // Act
        var result = registry.GetProvider("nonexistent");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void GetAllProviders_ShouldReturnAllProviders()
    {
        // Arrange
        var provider1 = new MockPriceProvider { BrokerId = "broker1" };
        var provider2 = new MockPriceProvider { BrokerId = "broker2" };
        var registry = new BrokerRegistry(new[] { provider1, provider2 });

        // Act
        var result = registry.GetAllProviders();

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(provider1);
        result.Should().Contain(provider2);
    }

    [Fact]
    public void GetAvailableBrokerIds_ShouldReturnAllBrokerIds()
    {
        // Arrange
        var providers = new[]
        {
            new MockPriceProvider { BrokerId = "broker1" },
            new MockPriceProvider { BrokerId = "broker2" },
            new MockPriceProvider { BrokerId = "broker3" }
        };
        var registry = new BrokerRegistry(providers);

        // Act
        var result = registry.GetAvailableBrokerIds();

        // Assert
        result.Should().HaveCount(3);
        result.Should().Contain("broker1");
        result.Should().Contain("broker2");
        result.Should().Contain("broker3");
    }

    [Fact]
    public void Constructor_WithEmptyProviders_ShouldInitializeWithEmptyList()
    {
        // Arrange & Act
        var registry = new BrokerRegistry(Array.Empty<MockPriceProvider>());

        // Assert
        registry.GetAllProviders().Should().BeEmpty();
        registry.GetAvailableBrokerIds().Should().BeEmpty();
    }
}
