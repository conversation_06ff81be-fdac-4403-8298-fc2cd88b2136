using FluentAssertions;
using MarketDataService.Configuration;
using MarketDataService.Data;
using MarketDataService.Interfaces;
using MarketDataService.Models;
using MarketDataService.Services;
using MarketDataService.Tests.TestHelpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace MarketDataService.Tests.Services;

public class PriceServiceTests : IDisposable
{
    private readonly MarketDataContext _context;
    private readonly Mock<IBrokerRegistry> _mockRegistry;
    private readonly Mock<IRedisCacheService> _mockCache;
    private readonly Mock<IOptions<BrokerSettings>> _mockSettings;
    private readonly PriceService _priceService;

    public PriceServiceTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<MarketDataContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        _context = new MarketDataContext(options);

        // Setup mocks
        _mockRegistry = new Mock<IBrokerRegistry>();
        _mockCache = new Mock<IRedisCacheService>();
        _mockSettings = new Mock<IOptions<BrokerSettings>>();
        
        var brokerSettings = new BrokerSettings { CacheMinutes = 5 };
        _mockSettings.Setup(s => s.Value).Returns(brokerSettings);

        _priceService = new PriceService(_context, _mockRegistry.Object, _mockCache.Object, _mockSettings.Object);
    }

    [Fact]
    public async Task GetLatestPriceAsync_WithCachedData_ShouldReturnCachedResult()
    {
        // Arrange
        var symbol = "AAPL";
        var preferredBroker = "finnhub";
        var cachedData = TestDataBuilder.CreateMarketData(symbol, 150.00m);
        
        _mockCache.Setup(c => c.GetAsync<MarketData>($"price:{preferredBroker}:{symbol}"))
                  .ReturnsAsync(cachedData);

        // Act
        var result = await _priceService.GetLatestPriceAsync(symbol, preferredBroker);

        // Assert
        result.Should().NotBeNull();
        result!.Symbol.Should().Be(symbol);
        result.Price.Should().Be(150.00m);
        
        // Verify cache was checked but provider was not called
        _mockCache.Verify(c => c.GetAsync<MarketData>(It.IsAny<string>()), Times.Once);
        _mockRegistry.Verify(r => r.GetProvider(It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task GetLatestPriceAsync_WithBypassCache_ShouldSkipCacheAndFetchFromProvider()
    {
        // Arrange
        var symbol = "AAPL";
        var preferredBroker = "finnhub";
        var mockProvider = new MockPriceProvider { BrokerId = preferredBroker, PriceToReturn = 155.00m };
        
        _mockRegistry.Setup(r => r.GetProvider(preferredBroker))
                    .Returns(mockProvider);

        // Act
        var result = await _priceService.GetLatestPriceAsync(symbol, preferredBroker, bypassCache: true);

        // Assert
        result.Should().NotBeNull();
        result!.Symbol.Should().Be(symbol.ToUpper());
        result.Price.Should().Be(155.00m);
        
        // Verify cache was not checked
        _mockCache.Verify(c => c.GetAsync<MarketData>(It.IsAny<string>()), Times.Never);
        
        // Verify data was saved to database and cache
        var savedData = await _context.MarketDataEntries.FirstOrDefaultAsync(m => m.Symbol == symbol.ToUpper());
        savedData.Should().NotBeNull();
        savedData!.Price.Should().Be(155.00m);
    }

    [Fact]
    public async Task GetLatestPriceAsync_WithNoCacheAndSuccessfulProvider_ShouldFetchAndCache()
    {
        // Arrange
        var symbol = "AAPL";
        var preferredBroker = "finnhub";
        var mockProvider = new MockPriceProvider { BrokerId = preferredBroker, PriceToReturn = 160.00m };
        
        _mockCache.Setup(c => c.GetAsync<MarketData>(It.IsAny<string>()))
                  .ReturnsAsync((MarketData?)null);
        _mockRegistry.Setup(r => r.GetProvider(preferredBroker))
                    .Returns(mockProvider);

        // Act
        var result = await _priceService.GetLatestPriceAsync(symbol, preferredBroker);

        // Assert
        result.Should().NotBeNull();
        result!.Symbol.Should().Be(symbol.ToUpper());
        result.Price.Should().Be(160.00m);
        
        // Verify cache was set
        _mockCache.Verify(c => c.SetAsync(
            $"price:{preferredBroker}:{symbol.ToUpper()}",
            It.IsAny<MarketData>(),
            TimeSpan.FromMinutes(5)), Times.Once);
    }

    [Fact]
    public async Task GetLatestPriceAsync_WithFailedPreferredProvider_ShouldFallbackToOtherProviders()
    {
        // Arrange
        var symbol = "AAPL";
        var preferredBroker = "polygon";
        var fallbackProvider = new MockPriceProvider { BrokerId = "finnhub", PriceToReturn = 165.00m };
        
        _mockCache.Setup(c => c.GetAsync<MarketData>(It.IsAny<string>()))
                  .ReturnsAsync((MarketData?)null);
        
        // Preferred provider returns null (failure)
        _mockRegistry.Setup(r => r.GetProvider(preferredBroker))
                    .Returns((MockPriceProvider?)null);
        
        // Fallback provider succeeds
        _mockRegistry.Setup(r => r.GetProvider("finnhub"))
                    .Returns(fallbackProvider);

        // Act
        var result = await _priceService.GetLatestPriceAsync(symbol, preferredBroker);

        // Assert
        result.Should().NotBeNull();
        result!.Symbol.Should().Be(symbol.ToUpper());
        result.Price.Should().Be(165.00m);
        
        // Verify both providers were tried
        _mockRegistry.Verify(r => r.GetProvider(preferredBroker), Times.Once);
        _mockRegistry.Verify(r => r.GetProvider("finnhub"), Times.Once);
    }

    [Fact]
    public async Task GetLatestPriceAsync_WithAllProvidersFailing_ShouldReturnNull()
    {
        // Arrange
        var symbol = "AAPL";
        var preferredBroker = "polygon";
        
        _mockCache.Setup(c => c.GetAsync<MarketData>(It.IsAny<string>()))
                  .ReturnsAsync((MarketData?)null);
        
        // All providers return null
        _mockRegistry.Setup(r => r.GetProvider(It.IsAny<string>()))
                    .Returns((MockPriceProvider?)null);

        // Act
        var result = await _priceService.GetLatestPriceAsync(symbol, preferredBroker);

        // Assert
        result.Should().BeNull();
        
        // Verify no data was saved
        var savedData = await _context.MarketDataEntries.FirstOrDefaultAsync();
        savedData.Should().BeNull();
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
