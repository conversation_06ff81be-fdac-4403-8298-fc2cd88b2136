using System.Net;
using AssistantService.Services;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Moq;

namespace AssistantService.Tests.TestHelpers;

public class TestWebApplicationFactory : WebApplicationFactory<Program>
{
    public Mock<IOllamaClient> MockOllamaClient { get; private set; } = null!;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing IOllamaClient registration
            var ollamaClientDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IOllamaClient));
            if (ollamaClientDescriptor != null)
                services.Remove(ollamaClientDescriptor);

            // Create a mock IOllamaClient directly instead of trying to mock HttpClient
            MockOllamaClient = new Mock<IOllamaClient>();
            services.AddSingleton(MockOllamaClient.Object);
        });

        builder.UseEnvironment("Testing");
    }

    public void SetupMockResponse(string response, HttpStatusCode statusCode = HttpStatusCode.OK)
    {
        if (MockOllamaClient == null)
            throw new InvalidOperationException("MockOllamaClient is not initialized");

        MockOllamaClient
            .Setup(x => x.AskAsync(It.IsAny<string>()))
            .ReturnsAsync(response);
    }

    public void SetupMockException(Exception exception)
    {
        if (MockOllamaClient == null)
            throw new InvalidOperationException("MockOllamaClient is not initialized");

        MockOllamaClient
            .Setup(x => x.AskAsync(It.IsAny<string>()))
            .ThrowsAsync(exception);
    }

    public void VerifyOllamaCall(Times times)
    {
        if (MockOllamaClient == null)
            throw new InvalidOperationException("MockOllamaClient is not initialized");

        MockOllamaClient
            .Verify(x => x.AskAsync(It.IsAny<string>()), times);
    }

    public void ResetMock()
    {
        MockOllamaClient?.Reset();
    }
}
