using System.Net;
using System.Net.Http.Json;
using AssistantService.Models;
using AssistantService.Tests.TestHelpers;
using FluentAssertions;
using Moq;
using Xunit;

namespace AssistantService.Tests.EndToEnd;

public class AssistantServiceE2ETests : IClassFixture<TestWebApplicationFactory>
{
    private readonly TestWebApplicationFactory _factory;
    private readonly HttpClient _client;

    public AssistantServiceE2ETests(TestWebApplicationFactory factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task CompleteWorkflow_AskQuestion_ReturnsExpectedResponse()
    {
        // Arrange
        _factory.ResetMock();
        const string prompt = "What is artificial intelligence?";
        const string expectedResponse = "Artificial intelligence (AI) is a branch of computer science...";

        _factory.SetupMockResponse(expectedResponse);
        
        var request = new AskRequest { Prompt = prompt };

        // Act
        var response = await _client.PostAsJsonAsync("/api/assistant/ask", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var askResponse = await response.Content.ReadFromJsonAsync<AskResponse>();
        askResponse.Should().NotBeNull();
        askResponse!.Reply.Should().Be(expectedResponse);
        
        // Verify that the Ollama call was made exactly once
        _factory.VerifyOllamaCall(Times.Once());
    }

    [Fact]
    public async Task ErrorHandling_OllamaServiceDown_ReturnsInternalServerError()
    {
        // Arrange
        _factory.ResetMock();
        _factory.SetupMockException(new HttpRequestException("Connection refused"));
        
        var request = new AskRequest { Prompt = "Test prompt" };

        // Act
        var response = await _client.PostAsJsonAsync("/api/assistant/ask", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.InternalServerError);
    }

    [Fact]
    public async Task Performance_MultipleRequests_HandledCorrectly()
    {
        // Arrange
        _factory.ResetMock();
        _factory.SetupMockResponse("Response");
        
        var tasks = new List<Task<HttpResponseMessage>>();
        
        // Act - Send 10 concurrent requests
        for (int i = 0; i < 10; i++)
        {
            var request = new AskRequest { Prompt = $"Question {i}" };
            tasks.Add(_client.PostAsJsonAsync("/api/assistant/ask", request));
        }
        
        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.Should().HaveCount(10);
        responses.Should().OnlyContain(r => r.StatusCode == HttpStatusCode.OK);
        
        // Verify all responses contain expected data
        foreach (var response in responses)
        {
            var askResponse = await response.Content.ReadFromJsonAsync<AskResponse>();
            askResponse.Should().NotBeNull();
            askResponse!.Reply.Should().Be("Response");
        }
    }

    [Theory]
    [InlineData("Simple question")]
    [InlineData("What is the weather like today?")]
    [InlineData("Explain quantum computing in simple terms")]
    [InlineData("Tell me a joke about programming")]
    [InlineData("How do I bake a chocolate cake?")]
    public async Task VariousPrompts_AllReturnValidResponses(string prompt)
    {
        // Arrange
        _factory.ResetMock();
        const string expectedResponse = "Mock response for any prompt";
        _factory.SetupMockResponse(expectedResponse);
        
        var request = new AskRequest { Prompt = prompt };

        // Act
        var response = await _client.PostAsJsonAsync("/api/assistant/ask", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var askResponse = await response.Content.ReadFromJsonAsync<AskResponse>();
        askResponse.Should().NotBeNull();
        askResponse!.Reply.Should().Be(expectedResponse);
    }

    [Fact]
    public async Task LongPrompt_HandledCorrectly()
    {
        // Arrange
        _factory.ResetMock();
        var longPrompt = string.Join(" ", Enumerable.Repeat("This is a very long prompt.", 100));
        const string expectedResponse = "Response to long prompt";

        _factory.SetupMockResponse(expectedResponse);
        
        var request = new AskRequest { Prompt = longPrompt };

        // Act
        var response = await _client.PostAsJsonAsync("/api/assistant/ask", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var askResponse = await response.Content.ReadFromJsonAsync<AskResponse>();
        askResponse.Should().NotBeNull();
        askResponse!.Reply.Should().Be(expectedResponse);
    }

    [Fact]
    public async Task SpecialCharacters_HandledCorrectly()
    {
        // Arrange
        _factory.ResetMock();
        const string promptWithSpecialChars = "What about these characters: !@#$%^&*()_+-=[]{}|;':\",./<>? And unicode: 你好 🌍";
        const string expectedResponse = "Special characters handled correctly";

        _factory.SetupMockResponse(expectedResponse);
        
        var request = new AskRequest { Prompt = promptWithSpecialChars };

        // Act
        var response = await _client.PostAsJsonAsync("/api/assistant/ask", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var askResponse = await response.Content.ReadFromJsonAsync<AskResponse>();
        askResponse.Should().NotBeNull();
        askResponse!.Reply.Should().Be(expectedResponse);
    }
}
