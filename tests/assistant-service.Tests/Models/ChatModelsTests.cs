using AssistantService.Models;
using FluentAssertions;
using Xunit;

namespace AssistantService.Tests.Models;

public class ChatModelsTests
{
    [Fact]
    public void ChatRequest_DefaultConstructor_InitializesCorrectly()
    {
        // Act
        var request = new ChatRequest();

        // Assert
        request.Model.Should().Be(string.Empty);
        request.Messages.Should().NotBeNull();
        request.Messages.Should().BeEmpty();
        request.Stream.Should().BeTrue();
    }

    [Fact]
    public void ChatMessage_DefaultConstructor_InitializesCorrectly()
    {
        // Act
        var message = new ChatMessage();

        // Assert
        message.Role.Should().Be(string.Empty);
        message.Content.Should().Be(string.Empty);
    }

    [Fact]
    public void ChatResponse_DefaultConstructor_InitializesCorrectly()
    {
        // Act
        var response = new ChatResponse();

        // Assert
        response.Message.Should().NotBeNull();
        response.Done.Should().BeFalse();
    }

    [Fact]
    public void StreamingChatResponse_DefaultConstructor_InitializesCorrectly()
    {
        // Act
        var response = new StreamingChatResponse();

        // Assert
        response.Message.Should().NotBeNull();
        response.Done.Should().BeFalse();
    }

    [Fact]
    public void ChatRequest_CanSetProperties()
    {
        // Arrange
        const string model = "mistral";
        var messages = new List<ChatMessage>
        {
            new() { Role = "user", Content = "Hello" }
        };

        // Act
        var request = new ChatRequest
        {
            Model = model,
            Messages = messages,
            Stream = false
        };

        // Assert
        request.Model.Should().Be(model);
        request.Messages.Should().BeSameAs(messages);
        request.Stream.Should().BeFalse();
    }

    [Fact]
    public void ChatMessage_CanSetProperties()
    {
        // Arrange
        const string role = "user";
        const string content = "Hello, world!";

        // Act
        var message = new ChatMessage
        {
            Role = role,
            Content = content
        };

        // Assert
        message.Role.Should().Be(role);
        message.Content.Should().Be(content);
    }

    [Fact]
    public void ChatResponse_CanSetProperties()
    {
        // Arrange
        var message = new ChatMessage { Role = "assistant", Content = "Hello!" };

        // Act
        var response = new ChatResponse
        {
            Message = message,
            Done = true
        };

        // Assert
        response.Message.Should().BeSameAs(message);
        response.Done.Should().BeTrue();
    }

    [Theory]
    [InlineData("user")]
    [InlineData("assistant")]
    [InlineData("system")]
    public void ChatMessage_WithVariousRoles_StoresCorrectly(string role)
    {
        // Act
        var message = new ChatMessage { Role = role, Content = "Test content" };

        // Assert
        message.Role.Should().Be(role);
    }

    [Theory]
    [InlineData("")]
    [InlineData("Short message")]
    [InlineData("This is a much longer message that contains multiple sentences and should still work correctly.")]
    [InlineData("Special characters: !@#$%^&*()_+-=[]{}|;':\",./<>?")]
    [InlineData("Unicode characters: 你好世界 🌍 🚀")]
    public void ChatMessage_WithVariousContent_StoresCorrectly(string content)
    {
        // Act
        var message = new ChatMessage { Role = "user", Content = content };

        // Assert
        message.Content.Should().Be(content);
    }
}
