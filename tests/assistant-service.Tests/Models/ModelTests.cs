using AssistantService.Models;
using FluentAssertions;
using Xunit;

namespace AssistantService.Tests.Models;

public class ModelTests
{
    [Fact]
    public void AskRequest_DefaultConstructor_InitializesWithEmptyPrompt()
    {
        // Act
        var request = new AskRequest();

        // Assert
        request.Prompt.Should().Be(string.Empty);
    }

    [Fact]
    public void AskRequest_CanSetPrompt()
    {
        // Arrange
        const string expectedPrompt = "What is the meaning of life?";
        
        // Act
        var request = new AskRequest { Prompt = expectedPrompt };

        // Assert
        request.Prompt.Should().Be(expectedPrompt);
    }

    [Fact]
    public void AskResponse_DefaultConstructor_InitializesWithEmptyReply()
    {
        // Act
        var response = new AskResponse();

        // Assert
        response.Reply.Should().Be(string.Empty);
    }

    [Fact]
    public void AskResponse_CanSetReply()
    {
        // Arrange
        const string expectedReply = "42";
        
        // Act
        var response = new AskResponse { Reply = expectedReply };

        // Assert
        response.Reply.Should().Be(expectedReply);
    }

    [Theory]
    [InlineData("")]
    [InlineData("Short prompt")]
    [InlineData("This is a much longer prompt that contains multiple sentences and should still work correctly with our model.")]
    [InlineData("Special characters: !@#$%^&*()_+-=[]{}|;':\",./<>?")]
    [InlineData("Unicode characters: 你好世界 🌍 🚀")]
    public void AskRequest_WithVariousPrompts_StoresCorrectly(string prompt)
    {
        // Act
        var request = new AskRequest { Prompt = prompt };

        // Assert
        request.Prompt.Should().Be(prompt);
    }

    [Theory]
    [InlineData("")]
    [InlineData("Short reply")]
    [InlineData("This is a much longer reply that contains multiple sentences and should still work correctly with our model.")]
    [InlineData("Special characters: !@#$%^&*()_+-=[]{}|;':\",./<>?")]
    [InlineData("Unicode characters: 你好世界 🌍 🚀")]
    public void AskResponse_WithVariousReplies_StoresCorrectly(string reply)
    {
        // Act
        var response = new AskResponse { Reply = reply };

        // Assert
        response.Reply.Should().Be(reply);
    }

    [Fact]
    public void AskRequest_Prompt_IsNotNull()
    {
        // Act
        var request = new AskRequest();

        // Assert
        request.Prompt.Should().NotBeNull();
    }

    [Fact]
    public void AskResponse_Reply_IsNotNull()
    {
        // Act
        var response = new AskResponse();

        // Assert
        response.Reply.Should().NotBeNull();
    }
}
