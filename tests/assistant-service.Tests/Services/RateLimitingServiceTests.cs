using AssistantService.Services;
using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AssistantService.Tests.Services;

public class RateLimitingServiceTests
{
    private readonly RateLimitingService _service;
    private readonly Mock<ILogger<RateLimitingService>> _loggerMock;
    private readonly IMemoryCache _cache;

    public RateLimitingServiceTests()
    {
        _cache = new MemoryCache(new MemoryCacheOptions());
        _loggerMock = new Mock<ILogger<RateLimitingService>>();
        _service = new RateLimitingService(_cache, _loggerMock.Object);
    }

    [Fact]
    public void CheckRateLimit_WithEmptyClientId_ReturnsNotAllowed()
    {
        // Act
        var result = _service.CheckRateLimit(string.Empty, "/api/chat");

        // Assert
        result.IsAllowed.Should().BeFalse();
        result.Reason.Should().Be("Client ID is required");
        result.LimitType.Should().Be(RateLimitType.Blocked);
    }

    [Fact]
    public void CheckRateLimit_WithinLimits_ReturnsAllowed()
    {
        // Arrange
        const string clientId = "test-client";
        const string endpoint = "/api/chat";

        // Act
        var result = _service.CheckRateLimit(clientId, endpoint);

        // Assert
        result.IsAllowed.Should().BeTrue();
        result.LimitType.Should().Be(RateLimitType.None);
        result.RequestsRemaining.Should().BeGreaterThan(0);
    }

    [Fact]
    public void CheckRateLimit_ExceedsPerMinuteLimit_ReturnsNotAllowed()
    {
        // Arrange
        const string clientId = "test-client";
        const string endpoint = "/api/chat";

        // Simulate 10 requests (the per-minute limit for /api/chat)
        for (int i = 0; i < 10; i++)
        {
            _service.RecordRequest(clientId, endpoint);
        }

        // Act
        var result = _service.CheckRateLimit(clientId, endpoint);

        // Assert
        result.IsAllowed.Should().BeFalse();
        result.LimitType.Should().Be(RateLimitType.PerMinute);
        result.Reason.Should().Be("Too many requests per minute");
    }

    [Fact]
    public void RecordRequest_IncrementsCounters()
    {
        // Arrange
        const string clientId = "test-client";
        const string endpoint = "/api/chat";

        // Act
        _service.RecordRequest(clientId, endpoint);
        var result = _service.CheckRateLimit(clientId, endpoint);

        // Assert
        result.RequestsRemaining.Should().Be(9); // 10 - 1 = 9 remaining
    }

    [Fact]
    public void BlockClient_PreventsRequests()
    {
        // Arrange
        const string clientId = "test-client";
        const string endpoint = "/api/chat";
        const string reason = "Test block";

        // Act
        _service.BlockClient(clientId, TimeSpan.FromMinutes(5), reason);
        var result = _service.CheckRateLimit(clientId, endpoint);

        // Assert
        result.IsAllowed.Should().BeFalse();
        result.LimitType.Should().Be(RateLimitType.Blocked);
        result.Reason.Should().Contain(reason);
    }

    [Fact]
    public void IsClientBlocked_WithBlockedClient_ReturnsTrue()
    {
        // Arrange
        const string clientId = "test-client";

        // Act
        _service.BlockClient(clientId, TimeSpan.FromMinutes(5), "Test");
        var result = _service.IsClientBlocked(clientId);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsClientBlocked_WithNonBlockedClient_ReturnsFalse()
    {
        // Arrange
        const string clientId = "test-client";

        // Act
        var result = _service.IsClientBlocked(clientId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsClientBlocked_WithExpiredBlock_ReturnsFalse()
    {
        // Arrange
        const string clientId = "test-client";

        // Act
        _service.BlockClient(clientId, TimeSpan.FromMilliseconds(1), "Test");
        Thread.Sleep(10); // Wait for block to expire
        var result = _service.IsClientBlocked(clientId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void CheckRateLimit_WithDifferentEndpoints_HasDifferentLimits()
    {
        // Arrange
        const string clientId = "test-client";
        const string chatEndpoint = "/api/chat";
        const string assistantEndpoint = "/api/assistant/ask";

        // Exhaust chat endpoint limit (10 requests)
        for (int i = 0; i < 10; i++)
        {
            _service.RecordRequest(clientId, chatEndpoint);
        }

        // Act
        var chatResult = _service.CheckRateLimit(clientId, chatEndpoint);
        var assistantResult = _service.CheckRateLimit(clientId, assistantEndpoint);

        // Assert
        chatResult.IsAllowed.Should().BeFalse();
        assistantResult.IsAllowed.Should().BeTrue(); // Different endpoint, different limit
    }

    [Fact]
    public void RecordRequest_WithExcessiveRequests_AutoBlocksClient()
    {
        // Arrange
        const string clientId = "test-client";
        const string endpoint = "/api/chat";

        // Act - Make more than 50 requests to trigger auto-block
        for (int i = 0; i < 55; i++)
        {
            _service.RecordRequest(clientId, endpoint);
        }

        var result = _service.IsClientBlocked(clientId);

        // Assert
        result.Should().BeTrue();
    }

    [Theory]
    [InlineData("/api/chat")]
    [InlineData("/api/assistant/ask")]
    public void CheckRateLimit_WithKnownEndpoints_UsesCorrectLimits(string endpoint)
    {
        // Arrange
        const string clientId = "test-client";

        // Act
        var result = _service.CheckRateLimit(clientId, endpoint);

        // Assert
        result.IsAllowed.Should().BeTrue();
        result.RequestsRemaining.Should().BeGreaterThan(0);
    }

    [Fact]
    public void CheckRateLimit_WithUnknownEndpoint_UsesDefaultLimits()
    {
        // Arrange
        const string clientId = "test-client";
        const string endpoint = "/api/unknown";

        // Act
        var result = _service.CheckRateLimit(clientId, endpoint);

        // Assert
        result.IsAllowed.Should().BeTrue();
        result.RequestsRemaining.Should().Be(5); // Default limit is 5 per minute
    }

    [Fact]
    public void RecordRequest_WithEmptyClientId_DoesNotThrow()
    {
        // Act & Assert
        var action = () => _service.RecordRequest(string.Empty, "/api/chat");
        action.Should().NotThrow();
    }
}
