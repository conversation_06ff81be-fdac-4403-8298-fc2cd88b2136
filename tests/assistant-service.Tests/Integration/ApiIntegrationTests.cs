using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using AssistantService.Models;
using AssistantService.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Moq.Protected;
using Xunit;

namespace AssistantService.Tests.Integration;

public class ApiIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public ApiIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task HealthCheck_ReturnsHealthy()
    {
        // Act
        var response = await _client.GetAsync("/");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound); // No root endpoint defined
    }

    [Fact]
    public async Task SwaggerEndpoint_IsAccessible()
    {
        // Act
        var response = await _client.GetAsync("/swagger/index.html");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task Ask_WithValidRequest_ReturnsOk()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing IOllamaClient registration
                var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IOllamaClient));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add mock IOllamaClient
                var mockHttpMessageHandler = new Mock<HttpMessageHandler>();
                var ollamaResponse = new {
                    message = new { role = "assistant", content = "Mocked response from Ollama" },
                    done = true
                };
                var responseContent = JsonSerializer.Serialize(ollamaResponse);

                mockHttpMessageHandler
                    .Protected()
                    .Setup<Task<HttpResponseMessage>>(
                        "SendAsync",
                        ItExpr.IsAny<HttpRequestMessage>(),
                        ItExpr.IsAny<CancellationToken>())
                    .ReturnsAsync(new HttpResponseMessage
                    {
                        StatusCode = HttpStatusCode.OK,
                        Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
                    });

                var mockHttpClient = new HttpClient(mockHttpMessageHandler.Object);
                var mockConfiguration = new Mock<IConfiguration>();
                mockConfiguration.Setup(c => c["OLLAMA_URL"]).Returns("http://test-ollama:11434/api/generate");
                services.AddSingleton<IOllamaClient>(new OllamaClient(mockHttpClient, mockConfiguration.Object));
            });
        }).CreateClient();

        var request = new AskRequest { Prompt = "Hello, world!" };

        // Act
        var response = await client.PostAsJsonAsync("/api/assistant/ask", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var askResponse = await response.Content.ReadFromJsonAsync<AskResponse>();
        askResponse.Should().NotBeNull();
        askResponse!.Reply.Should().Be("Mocked response from Ollama");
    }

    [Fact]
    public async Task Ask_WithEmptyPrompt_ReturnsOk()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing IOllamaClient registration
                var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IOllamaClient));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add mock IOllamaClient
                var mockHttpMessageHandler = new Mock<HttpMessageHandler>();
                var ollamaResponse = new {
                    message = new { role = "assistant", content = "Please provide a prompt" },
                    done = true
                };
                var responseContent = JsonSerializer.Serialize(ollamaResponse);

                mockHttpMessageHandler
                    .Protected()
                    .Setup<Task<HttpResponseMessage>>(
                        "SendAsync",
                        ItExpr.IsAny<HttpRequestMessage>(),
                        ItExpr.IsAny<CancellationToken>())
                    .ReturnsAsync(new HttpResponseMessage
                    {
                        StatusCode = HttpStatusCode.OK,
                        Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
                    });

                var mockHttpClient = new HttpClient(mockHttpMessageHandler.Object);
                var mockConfiguration = new Mock<IConfiguration>();
                mockConfiguration.Setup(c => c["OLLAMA_URL"]).Returns("http://test-ollama:11434/api/generate");
                services.AddSingleton<IOllamaClient>(new OllamaClient(mockHttpClient, mockConfiguration.Object));
            });
        }).CreateClient();

        var request = new AskRequest { Prompt = "" };

        // Act
        var response = await client.PostAsJsonAsync("/api/assistant/ask", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var errorMessage = await response.Content.ReadAsStringAsync();
        errorMessage.Should().Be("Prompt is required");
    }

    [Fact]
    public async Task Ask_WithInvalidJson_ReturnsBadRequest()
    {
        // Arrange
        var invalidJson = "{ invalid json }";
        var content = new StringContent(invalidJson, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PostAsync("/api/assistant/ask", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task Ask_WithMissingContentType_ReturnsBadRequest()
    {
        // Arrange
        var jsonContent = JsonSerializer.Serialize(new AskRequest { Prompt = "Test" });
        var content = new StringContent(jsonContent, Encoding.UTF8); // No content type

        // Act
        var response = await _client.PostAsync("/api/assistant/ask", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.UnsupportedMediaType);
    }

    [Fact]
    public async Task Chat_WithValidRequest_ReturnsOk()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing IOllamaClient registration
                var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IOllamaClient));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add mock IOllamaClient
                var mockOllamaClient = new Mock<IOllamaClient>();
                mockOllamaClient
                    .Setup(x => x.AskAsync(It.IsAny<string>()))
                    .ReturnsAsync("Mocked chat response from Ollama");

                services.AddSingleton(mockOllamaClient.Object);
            });
        }).CreateClient();

        var request = new ChatRequest
        {
            Model = "mistral",
            Messages = new List<ChatMessage>
            {
                new() { Role = "user", Content = "Hello, world!" }
            },
            Stream = false
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/chat", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var chatResponse = await response.Content.ReadFromJsonAsync<ChatResponse>();
        chatResponse.Should().NotBeNull();
        chatResponse!.Message.Role.Should().Be("assistant");
        chatResponse.Message.Content.Should().Be("Mocked chat response from Ollama");
        chatResponse.Done.Should().BeTrue();
    }
}
