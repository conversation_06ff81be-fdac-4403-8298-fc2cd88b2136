using System.Security.Claims;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Newtonsoft.Json;
using Xunit;
using auth_service.Controllers;
using auth_service.Models;
using auth_service.Services;

namespace AuthService.Tests.Controllers;

public class AuthControllerTests
{
    private readonly Mock<ISupabaseClient> _supabaseClientMock;
    private readonly AuthController _controller;

    public AuthControllerTests()
    {
        _supabaseClientMock = new Mock<ISupabaseClient>();
        _controller = new AuthController(_supabaseClientMock.Object);
    }

    [Fact]
    public async Task GetProfile_WithValidUserId_ReturnsOkWithProfile()
    {
        // Arrange
        const string userId = "test-user-id";
        var expectedUser = new SupabaseUser
        {
            Id = userId,
            Email = "<EMAIL>",
            Phone = "+1234567890",
            EmailConfirmedAt = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            UserMetadata = new Dictionary<string, object>
            {
                ["full_name"] = "Test User",
                ["username"] = "testuser",
                ["broker_id"] = "broker123"
            }
        };

        var claims = new List<Claim>
        {
            new("sub", userId)
        };
        var identity = new ClaimsIdentity(claims, "test");
        var principal = new ClaimsPrincipal(identity);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = principal }
        };

        _supabaseClientMock
            .Setup(x => x.GetProfileAsync(userId))
            .ReturnsAsync(expectedUser);

        // Act
        var result = await _controller.GetProfile();

        // Assert
        result.Should().BeOfType<OkObjectResult>();

        var okResult = result as OkObjectResult;
        okResult!.Value.Should().NotBeNull();

        _supabaseClientMock.Verify(x => x.GetProfileAsync(userId), Times.Once);
    }

    [Fact]
    public async Task GetProfile_WithNoSubClaim_ReturnsUnauthorized()
    {
        // Arrange
        var claims = new List<Claim>
        {
            new("email", "<EMAIL>")
        };
        var identity = new ClaimsIdentity(claims, "test");
        var principal = new ClaimsPrincipal(identity);
        
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = principal }
        };

        // Act
        var result = await _controller.GetProfile();

        // Assert
        result.Should().BeOfType<UnauthorizedResult>();
        
        _supabaseClientMock.Verify(x => x.GetProfileAsync(It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task GetProfile_WithNullProfile_ReturnsNotFound()
    {
        // Arrange
        const string userId = "test-user-id";
        
        var claims = new List<Claim>
        {
            new("sub", userId)
        };
        var identity = new ClaimsIdentity(claims, "test");
        var principal = new ClaimsPrincipal(identity);
        
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = principal }
        };

        _supabaseClientMock
            .Setup(x => x.GetProfileAsync(userId))
            .ReturnsAsync(null as SupabaseUser);

        // Act
        var result = await _controller.GetProfile();

        // Assert
        result.Should().BeOfType<NotFoundObjectResult>();
        
        var notFoundResult = result as NotFoundObjectResult;
        notFoundResult!.Value.Should().Be("User not found");
        
        _supabaseClientMock.Verify(x => x.GetProfileAsync(userId), Times.Once);
    }

    [Fact]
    public async Task GetProfile_WithEmptyUserId_ReturnsUnauthorized()
    {
        // Arrange
        var claims = new List<Claim>
        {
            new("sub", "")
        };
        var identity = new ClaimsIdentity(claims, "test");
        var principal = new ClaimsPrincipal(identity);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = principal }
        };

        // Act
        var result = await _controller.GetProfile();

        // Assert
        result.Should().BeOfType<UnauthorizedResult>();

        _supabaseClientMock.Verify(x => x.GetProfileAsync(It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task GetProfile_WithWhitespaceUserId_ReturnsUnauthorized()
    {
        // Arrange
        var claims = new List<Claim>
        {
            new("sub", "   ")  // Whitespace user ID
        };
        var identity = new ClaimsIdentity(claims, "test");
        var principal = new ClaimsPrincipal(identity);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = principal }
        };

        // Act
        var result = await _controller.GetProfile();

        // Assert
        result.Should().BeOfType<UnauthorizedResult>();

        _supabaseClientMock.Verify(x => x.GetProfileAsync(It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task GetProfile_WhenSupabaseThrowsException_ReturnsInternalServerError()
    {
        // Arrange
        const string userId = "test-user-id";
        
        var claims = new List<Claim>
        {
            new("sub", userId)
        };
        var identity = new ClaimsIdentity(claims, "test");
        var principal = new ClaimsPrincipal(identity);
        
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = principal }
        };

        _supabaseClientMock
            .Setup(x => x.GetProfileAsync(userId))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetProfile());
        
        _supabaseClientMock.Verify(x => x.GetProfileAsync(userId), Times.Once);
    }

    // Authentication Tests
    [Fact]
    public async Task Signup_WithValidRequest_ReturnsOkWithAuthResponse()
    {
        // Arrange
        var request = new SignupRequest
        {
            Email = "<EMAIL>",
            Password = "password123"
        };

        var expectedResponse = new AuthResponse
        {
            AccessToken = "access_token",
            RefreshToken = "refresh_token",
            TokenType = "bearer",
            ExpiresIn = 3600
        };

        _supabaseClientMock
            .Setup(x => x.SignupAsync(It.IsAny<SignupRequest>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.Signup(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(expectedResponse);
    }

    [Fact]
    public async Task Signup_WithInvalidRequest_ReturnsBadRequest()
    {
        // Arrange
        var request = new SignupRequest
        {
            Email = "",
            Password = "password123"
        };

        // Act
        var result = await _controller.Signup(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task Login_WithValidRequest_ReturnsOkWithAuthResponse()
    {
        // Arrange
        var request = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "password123"
        };

        var expectedResponse = new AuthResponse
        {
            AccessToken = "access_token",
            RefreshToken = "refresh_token",
            TokenType = "bearer",
            ExpiresIn = 3600
        };

        _supabaseClientMock
            .Setup(x => x.LoginAsync(It.IsAny<LoginRequest>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.Login(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(expectedResponse);
    }

    [Fact]
    public async Task RefreshToken_WithValidRequest_ReturnsOkWithAuthResponse()
    {
        // Arrange
        var request = new TokenRefreshRequest
        {
            RefreshToken = "refresh_token"
        };

        var expectedResponse = new AuthResponse
        {
            AccessToken = "new_access_token",
            RefreshToken = "new_refresh_token",
            TokenType = "bearer",
            ExpiresIn = 3600
        };

        _supabaseClientMock
            .Setup(x => x.RefreshTokenAsync(It.IsAny<TokenRefreshRequest>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.RefreshToken(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(expectedResponse);
    }

    [Fact]
    public async Task RecoverPassword_WithValidEmail_ReturnsOkWithSuccessResponse()
    {
        // Arrange
        var request = new PasswordRecoveryRequest
        {
            Email = "<EMAIL>"
        };

        var expectedResponse = new SuccessResponse
        {
            Message = "Password recovery email sent"
        };

        _supabaseClientMock
            .Setup(x => x.RecoverPasswordAsync(It.IsAny<PasswordRecoveryRequest>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.RecoverPassword(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(expectedResponse);
    }

    [Fact]
    public async Task SendOtp_WithValidRequest_ReturnsOkWithSuccessResponse()
    {
        // Arrange
        var request = new OtpRequest
        {
            Email = "<EMAIL>",
            CreateUser = true
        };

        var expectedResponse = new SuccessResponse
        {
            Message = "OTP sent successfully"
        };

        _supabaseClientMock
            .Setup(x => x.SendOtpAsync(It.IsAny<OtpRequest>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SendOtp(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(expectedResponse);
    }
}
