using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using FluentAssertions;
using Microsoft.IdentityModel.Tokens;
using Xunit;

namespace AuthService.Tests.Integration;

public class TokenValidationTests
{
    private const string TestJwtSecret = "test-jwt-secret-key-for-testing-purposes-only-not-for-production-use";
    private const string TestSupabaseUrl = "https://test.supabase.co";

    [Fact]
    public void GenerateTestToken_ShouldCreateValidJWT()
    {
        // Arrange
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(TestJwtSecret);
        var signingKey = new SymmetricSecurityKey(key);
        var credentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim("sub", "test-user-id"),
            new Claim("email", "<EMAIL>"),
            new Claim("role", "trader"),
            new Claim("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new Claim("exp", DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddHours(1),
            Issuer = $"{TestSupabaseUrl}/auth/v1",
            SigningCredentials = credentials
        };

        // Act
        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        // Assert
        tokenString.Should().NotBeNullOrEmpty();
        
        // Validate the token can be parsed
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = $"{TestSupabaseUrl}/auth/v1",
            ValidateAudience = false,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = signingKey,
            ClockSkew = TimeSpan.FromMinutes(5),
            RequireExpirationTime = true,
            RequireSignedTokens = true
        };

        // This should not throw
        var principal = tokenHandler.ValidateToken(tokenString, validationParameters, out var validatedToken);
        
        principal.Should().NotBeNull();
        principal.FindFirst("sub")?.Value.Should().Be("test-user-id");
        principal.FindFirst("email")?.Value.Should().Be("<EMAIL>");
    }

    [Fact]
    public void ValidateSupabaseTokenFormat_ShouldMatchExpectedStructure()
    {
        // This test documents what we expect from Supabase tokens
        // Based on Supabase documentation: https://supabase.com/docs/guides/auth/jwt

        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(TestJwtSecret);
        var signingKey = new SymmetricSecurityKey(key);
        var credentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

        // Supabase JWT structure
        var claims = new[]
        {
            new Claim("aud", "authenticated"), // Supabase audience
            new Claim("exp", DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new Claim("sub", "test-user-id"),
            new Claim("email", "<EMAIL>"),
            new Claim("phone", ""),
            new Claim("app_metadata", "{\"provider\":\"email\",\"providers\":[\"email\"]}", ClaimValueTypes.String),
            new Claim("user_metadata", "{}", ClaimValueTypes.String),
            new Claim("role", "authenticated"),
            new Claim("aal", "aal1"),
            new Claim("amr", "[{\"method\":\"password\",\"timestamp\":**********}]", ClaimValueTypes.String),
            new Claim("session_id", "test-session-id")
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddHours(1),
            Issuer = $"{TestSupabaseUrl}/auth/v1", // Supabase issuer format
            Audience = "authenticated", // Supabase uses this audience
            SigningCredentials = credentials
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        // Test validation with correct Supabase parameters
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = $"{TestSupabaseUrl}/auth/v1",
            ValidateAudience = true,
            ValidAudience = "authenticated", // Supabase uses "authenticated" as audience
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = signingKey,
            ClockSkew = TimeSpan.FromMinutes(5),
            RequireExpirationTime = true,
            RequireSignedTokens = true
        };

        var principal = tokenHandler.ValidateToken(tokenString, validationParameters, out var validatedToken);
        
        principal.Should().NotBeNull();
        principal.FindFirst("sub")?.Value.Should().Be("test-user-id");
        principal.FindFirst("email")?.Value.Should().Be("<EMAIL>");
        principal.FindFirst("aud")?.Value.Should().Be("authenticated");
    }

    [Fact]
    public void ValidateToken_WithWrongAudience_ShouldFail()
    {
        // Test that tokens with wrong audience are rejected
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(TestJwtSecret);
        var signingKey = new SymmetricSecurityKey(key);
        var credentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim("aud", "wrong-audience"), // Wrong audience
            new Claim("sub", "test-user-id"),
            new Claim("email", "<EMAIL>")
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddHours(1),
            Issuer = $"{TestSupabaseUrl}/auth/v1",
            Audience = "wrong-audience",
            SigningCredentials = credentials
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = $"{TestSupabaseUrl}/auth/v1",
            ValidateAudience = true,
            ValidAudience = "authenticated", // Expecting "authenticated"
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = signingKey,
            ClockSkew = TimeSpan.FromMinutes(5)
        };

        // This should throw a SecurityTokenInvalidAudienceException
        var action = () => tokenHandler.ValidateToken(tokenString, validationParameters, out var validatedToken);
        action.Should().Throw<SecurityTokenInvalidAudienceException>();
    }

    [Fact]
    public void ValidateToken_WithWrongIssuer_ShouldFail()
    {
        // Test that tokens with wrong issuer are rejected
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(TestJwtSecret);
        var signingKey = new SymmetricSecurityKey(key);
        var credentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim("aud", "authenticated"),
            new Claim("sub", "test-user-id"),
            new Claim("email", "<EMAIL>")
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddHours(1),
            Issuer = "https://wrong-issuer.com/auth/v1", // Wrong issuer
            Audience = "authenticated",
            SigningCredentials = credentials
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = $"{TestSupabaseUrl}/auth/v1", // Expecting correct issuer
            ValidateAudience = true,
            ValidAudience = "authenticated",
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = signingKey,
            ClockSkew = TimeSpan.FromMinutes(5)
        };

        // This should throw a SecurityTokenInvalidIssuerException
        var action = () => tokenHandler.ValidateToken(tokenString, validationParameters, out var validatedToken);
        action.Should().Throw<SecurityTokenInvalidIssuerException>();
    }
}
