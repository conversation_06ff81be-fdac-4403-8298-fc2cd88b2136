using auth_service.Models;
using auth_service.Services;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Moq;
using Moq.Protected;
using System.Net;
using System.Text;
using System.Text.Json;

namespace auth_service.Tests.Services;

public class SupabaseClientPhoneSignInTests
{
    private readonly SupabaseClient _supabaseClient;
    private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
    private readonly HttpClient _httpClient;
    private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;

    public SupabaseClientPhoneSignInTests()
    {
        _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_httpMessageHandlerMock.Object);
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();

        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Supabase:Url"] = "https://test.supabase.co",
                ["Supabase:Key"] = "test-key"
            })
            .Build();

        // Setup HttpClientFactory to return our mocked HttpClient
        _httpClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(_httpClient);

        _supabaseClient = new SupabaseClient(_httpClient, _httpClientFactoryMock.Object, configuration);
    }

    [Fact]
    public async Task SendPhoneOtpAsync_WithValidPhoneNumber_ReturnsSuccessResponse()
    {
        // Arrange
        var phoneNumber = "+1234567890";
        var expectedResponse = new SuccessResponse { Message = "OTP sent successfully" };

        var responseJson = JsonSerializer.Serialize(expectedResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Post &&
                    req.RequestUri!.ToString().Contains("/auth/v1/otp")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _supabaseClient.SendPhoneOtpAsync(phoneNumber);

        // Assert
        result.Should().NotBeNull();
        result.Message.Should().Be("OTP sent successfully");
    }

    [Fact]
    public async Task SendPhoneOtpAsync_WithCreateUserFalse_SendsCorrectPayload()
    {
        // Arrange
        var phoneNumber = "+1234567890";
        var createUser = false;
        var expectedResponse = new SuccessResponse { Message = "OTP sent successfully" };

        var responseJson = JsonSerializer.Serialize(expectedResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        HttpRequestMessage? capturedRequest = null;
        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Callback<HttpRequestMessage, CancellationToken>((req, _) => capturedRequest = req)
            .ReturnsAsync(httpResponse);

        // Act
        await _supabaseClient.SendPhoneOtpAsync(phoneNumber, createUser);

        // Assert
        capturedRequest.Should().NotBeNull();
        capturedRequest!.Method.Should().Be(HttpMethod.Post);
        capturedRequest.RequestUri!.ToString().Should().Contain("/auth/v1/otp");

        var requestContent = await capturedRequest.Content!.ReadAsStringAsync();
        var requestPayload = JsonSerializer.Deserialize<JsonElement>(requestContent);
        
        requestPayload.GetProperty("phone").GetString().Should().Be(phoneNumber);
        requestPayload.GetProperty("create_user").GetBoolean().Should().Be(createUser);
    }

    [Fact]
    public async Task SignInWithPhoneAsync_WithValidCredentials_ReturnsAuthResponse()
    {
        // Arrange
        var phoneNumber = "+1234567890";
        var otpCode = "123456";
        var expectedResponse = new AuthResponse
        {
            AccessToken = "test-access-token",
            RefreshToken = "test-refresh-token",
            TokenType = "bearer",
            ExpiresIn = 3600,
            User = new SupabaseUser
            {
                Id = "test-user-id",
                Phone = phoneNumber
            }
        };

        var responseJson = JsonSerializer.Serialize(expectedResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Post &&
                    req.RequestUri!.ToString().Contains("/auth/v1/verify")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _supabaseClient.SignInWithPhoneAsync(phoneNumber, otpCode);

        // Assert
        result.Should().NotBeNull();
        result.AccessToken.Should().Be("test-access-token");
        result.RefreshToken.Should().Be("test-refresh-token");
        result.User.Should().NotBeNull();
        result.User!.Phone.Should().Be(phoneNumber);
    }

    [Fact]
    public async Task SignInWithPhoneAsync_SendsCorrectVerifyPayload()
    {
        // Arrange
        var phoneNumber = "+1234567890";
        var otpCode = "123456";
        var expectedResponse = new AuthResponse
        {
            AccessToken = "test-access-token",
            RefreshToken = "test-refresh-token"
        };

        var responseJson = JsonSerializer.Serialize(expectedResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        HttpRequestMessage? capturedRequest = null;
        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Callback<HttpRequestMessage, CancellationToken>((req, _) => capturedRequest = req)
            .ReturnsAsync(httpResponse);

        // Act
        await _supabaseClient.SignInWithPhoneAsync(phoneNumber, otpCode);

        // Assert
        capturedRequest.Should().NotBeNull();
        capturedRequest!.Method.Should().Be(HttpMethod.Post);
        capturedRequest.RequestUri!.ToString().Should().Contain("/auth/v1/verify");

        var requestContent = await capturedRequest.Content!.ReadAsStringAsync();
        var requestPayload = JsonSerializer.Deserialize<JsonElement>(requestContent);
        
        requestPayload.GetProperty("phone").GetString().Should().Be(phoneNumber);
        requestPayload.GetProperty("token").GetString().Should().Be(otpCode);
        requestPayload.GetProperty("type").GetString().Should().Be("sms");
    }

    [Fact]
    public async Task SignInWithPhoneAsync_WithInvalidOtp_ThrowsHttpRequestException()
    {
        // Arrange
        var phoneNumber = "+1234567890";
        var otpCode = "invalid";
        var errorResponse = new AuthErrorResponse
        {
            Error = "invalid_otp",
            ErrorDescription = "Invalid OTP code",
            Message = "Invalid OTP code"
        };

        var responseJson = JsonSerializer.Serialize(errorResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(() => 
            _supabaseClient.SignInWithPhoneAsync(phoneNumber, otpCode));
        exception.Message.Should().Contain("OTP verification failed");
        exception.Message.Should().Contain("Invalid OTP code");
    }

    [Fact]
    public async Task SendPhoneOtpAsync_WithInvalidPhoneNumber_ThrowsHttpRequestException()
    {
        // Arrange
        var phoneNumber = "invalid-phone";
        var errorResponse = new AuthErrorResponse
        {
            Error = "invalid_phone",
            ErrorDescription = "Invalid phone number format",
            Message = "Invalid phone number format"
        };

        var responseJson = JsonSerializer.Serialize(errorResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(() =>
            _supabaseClient.SendPhoneOtpAsync(phoneNumber));
        exception.Message.Should().Contain("OTP send failed");
        exception.Message.Should().Contain("Invalid phone number format");
    }
}
