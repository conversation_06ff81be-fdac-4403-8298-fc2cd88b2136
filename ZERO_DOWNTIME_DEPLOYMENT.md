# Zero-Downtime Deployment Guide

This guide explains how to use the zero-downtime deployment system for the Abra API using blue-green deployment strategy with <PERSON>er Compose on a single GCE VM.

## Overview

The zero-downtime deployment system uses a **blue-green deployment strategy** where:
- Two identical environments (blue and green) run in parallel
- Only one environment serves live traffic at a time
- New deployments go to the inactive environment
- Traffic switches instantly after health checks pass
- The old environment is cleaned up after successful deployment

## Architecture

```
Internet → Nginx Proxy → [Blue Environment] or [Green Environment]
                      ↓
                   Shared Database (PostgreSQL + Redis)
```

### Components

1. **Blue Environment**: Services running on ports 8081-8084
2. **Green Environment**: Services running on ports 8091-8094
3. **Nginx Proxy**: Routes traffic based on upstream configuration
4. **Shared Infrastructure**: PostgreSQL and Redis shared between environments

## Files Structure

```
├── docker-compose.blue.yml          # Blue environment services
├── docker-compose.green.yml         # Green environment services
├── docker-compose.nginx.yml         # Nginx proxy configuration
├── nginx/
│   ├── nginx.blue-green.conf        # Main nginx configuration
│   ├── upstreams.blue.conf          # Blue environment upstreams
│   ├── upstreams.green.conf         # Green environment upstreams
│   └── upstreams.conf               # Active upstream configuration
├── deploy-zero-downtime.sh          # Main deployment script
├── health-monitor.sh                # Health monitoring utilities
├── rollback-recovery.sh             # Rollback and recovery tools
├── .current_environment             # Tracks active environment
└── .deployment_history              # Deployment history log
```

## Usage

### 1. GitHub Actions Deployment

The easiest way to deploy is through GitHub Actions:

```yaml
# Trigger deployment
git push origin main

# Or use manual trigger with options:
# - setup_ssl: Setup SSL certificates
# - force_ssl_renewal: Force SSL renewal
# - force_rollback: Rollback to previous environment
# - skip_health_checks: Skip health checks (not recommended)
```

### 2. Manual Deployment

```bash
# Deploy to the inactive environment
./deploy-zero-downtime.sh deploy

# Check deployment status
./deploy-zero-downtime.sh status

# Perform health check
./deploy-zero-downtime.sh health-check
```

### 3. Rollback Operations

```bash
# Quick rollback to previous environment
./rollback-recovery.sh rollback

# Emergency recovery (restores from backup)
./rollback-recovery.sh emergency

# Automated recovery with multiple strategies
./rollback-recovery.sh auto 3
```

### 4. Health Monitoring

```bash
# One-time health check
./health-monitor.sh check

# Continuous monitoring (60s intervals)
./health-monitor.sh monitor 60

# Performance check
./health-monitor.sh performance

# API endpoint testing
./health-monitor.sh api-test https://abraapp.undeclab.com

# Generate comprehensive report
./health-monitor.sh report
```

### 5. Backup and Recovery

```bash
# Create backup of current environment
./rollback-recovery.sh backup

# List available backups
./rollback-recovery.sh list-backups

# Restore from specific backup
./rollback-recovery.sh restore /path/to/backup

# View deployment history
./rollback-recovery.sh history
```

## Deployment Process

1. **Preparation**
   - Update code from Git repository
   - Create backup of current state
   - Set up environment configuration

2. **Build Phase**
   - Build new Docker images for inactive environment
   - Start services in inactive environment
   - Wait for services to initialize

3. **Health Check Phase**
   - Verify all services are healthy
   - Test database connectivity
   - Validate API endpoints

4. **Traffic Switch**
   - Update nginx upstream configuration
   - Reload nginx (zero-downtime)
   - Update environment tracking

5. **Cleanup**
   - Stop old environment services
   - Clean up unused Docker resources
   - Record deployment in history

## Monitoring and Alerts

### Health Check Endpoints

- **Main Health**: `https://abraapp.undeclab.com/health`
- **Service Health**: Each service exposes `/health` or `/health/ready`
- **Nginx Status**: Built-in nginx status monitoring

### Key Metrics

- Service response times
- Memory and CPU usage
- Database connection status
- SSL certificate validity
- Disk space usage

## Troubleshooting

### Common Issues

1. **Health Check Failures**
   ```bash
   # Check service logs
   docker compose -f docker-compose.blue.yml logs service-name
   
   # Manual health check
   curl -f http://localhost:8081/health
   ```

2. **SSL Certificate Issues**
   ```bash
   # Force SSL renewal
   ./setup-ssl.sh renew
   
   # Check certificate status
   openssl x509 -in certbot/conf/live/abraapp.undeclab.com/fullchain.pem -text -noout
   ```

3. **Database Connection Issues**
   ```bash
   # Check database status
   docker exec postgres pg_isready -U postgres -d abraapi
   
   # Check Redis status
   docker exec redis redis-cli ping
   ```

### Recovery Procedures

1. **Automatic Recovery**: The system attempts automatic recovery on deployment failures
2. **Manual Rollback**: Use `./rollback-recovery.sh rollback` for quick rollback
3. **Emergency Recovery**: Use `./rollback-recovery.sh emergency` for disaster recovery
4. **Backup Restore**: Restore from specific backup if needed

## Best Practices

1. **Always test in staging** before production deployment
2. **Monitor health checks** during and after deployment
3. **Keep backups** of working configurations
4. **Use SSL certificates** for production environments
5. **Monitor system resources** to prevent resource exhaustion
6. **Review deployment logs** for any warnings or errors

## Security Considerations

- SSL/TLS encryption for all traffic
- Rate limiting on API endpoints
- Security headers in nginx configuration
- Restricted access to deployment management endpoints
- Regular security updates for base images

## Performance Optimization

- Docker BuildKit for faster builds
- Nginx caching and compression
- Health check timeouts optimized for service startup times
- Resource limits to prevent resource exhaustion
- Efficient Docker layer caching

## Support

For issues or questions:
1. Check the deployment logs: `tail -f deployment.log`
2. Run health diagnostics: `./health-monitor.sh report`
3. Review deployment history: `./rollback-recovery.sh history`
4. Check system resources: `./health-monitor.sh performance`
