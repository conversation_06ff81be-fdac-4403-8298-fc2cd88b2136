#!/bin/bash

# Health monitoring and validation script for Abra API
# Provides comprehensive health checking and monitoring capabilities

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CURRENT_ENV_FILE="$SCRIPT_DIR/.current_environment"
MONITOR_LOG="$SCRIPT_DIR/health-monitor.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$MONITOR_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$MONITOR_LOG"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$MONITOR_LOG"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$MONITOR_LOG"
}

# Get current active environment
get_current_environment() {
    if [[ -f "$CURRENT_ENV_FILE" ]]; then
        cat "$CURRENT_ENV_FILE"
    else
        echo "blue"  # Default to blue
    fi
}

# Service health check with detailed response
detailed_health_check() {
    local service_name=$1
    local port=$2
    local endpoint=${3:-"/health"}
    
    log "Checking $service_name at port $port..."
    
    # Check if port is accessible
    if ! nc -z localhost "$port" 2>/dev/null; then
        error "$service_name: Port $port is not accessible"
        return 1
    fi
    
    # Make HTTP request and capture response
    local response
    local http_code
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "http://localhost:$port$endpoint" 2>/dev/null || echo "HTTPSTATUS:000")
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*$//')
    
    case "$http_code" in
        "200")
            success "$service_name: Healthy (HTTP $http_code)"
            if [[ -n "$body" && "$body" != "healthy" ]]; then
                log "$service_name response: $body"
            fi
            return 0
            ;;
        "000")
            error "$service_name: Connection failed"
            return 1
            ;;
        *)
            error "$service_name: Unhealthy (HTTP $http_code)"
            if [[ -n "$body" ]]; then
                log "$service_name error response: $body"
            fi
            return 1
            ;;
    esac
}

# Check database connectivity
check_database() {
    log "Checking database connectivity..."
    
    if docker exec postgres pg_isready -U "${POSTGRES_USER:-postgres}" -d "${POSTGRES_DB:-abraapi}" >/dev/null 2>&1; then
        success "PostgreSQL: Connected and ready"
        return 0
    else
        error "PostgreSQL: Connection failed"
        return 1
    fi
}

# Check Redis connectivity
check_redis() {
    log "Checking Redis connectivity..."
    
    if docker exec redis redis-cli ping | grep -q "PONG"; then
        success "Redis: Connected and responding"
        return 0
    else
        error "Redis: Connection failed"
        return 1
    fi
}

# Check nginx status
check_nginx() {
    log "Checking Nginx status..."
    
    if docker exec nginx-proxy nginx -t >/dev/null 2>&1; then
        success "Nginx: Configuration is valid"
    else
        error "Nginx: Configuration is invalid"
        return 1
    fi
    
    # Check if nginx is serving requests
    if curl -f -s "http://localhost/health" >/dev/null 2>&1; then
        success "Nginx: HTTP endpoint responding"
    else
        error "Nginx: HTTP endpoint not responding"
        return 1
    fi
    
    # Check HTTPS if certificates exist
    if [[ -f "certbot/conf/live/abraapp.undeclab.com/fullchain.pem" ]]; then
        if curl -f -s -k "https://localhost/health" >/dev/null 2>&1; then
            success "Nginx: HTTPS endpoint responding"
        else
            warning "Nginx: HTTPS endpoint not responding"
        fi
    fi
    
    return 0
}

# Comprehensive system health check
system_health_check() {
    local environment=${1:-$(get_current_environment)}
    local failed_checks=0
    
    log "Starting comprehensive health check for $environment environment..."
    
    # Check infrastructure services
    check_database || ((failed_checks++))
    check_redis || ((failed_checks++))
    check_nginx || ((failed_checks++))
    
    # Determine port range based on environment
    local base_port
    if [[ "$environment" == "blue" ]]; then
        base_port=8081
    else
        base_port=8091
    fi
    
    # Check application services
    detailed_health_check "Assistant Service" "$base_port" "/health" || ((failed_checks++))
    detailed_health_check "Auth Service" "$((base_port + 1))" "/health" || ((failed_checks++))
    detailed_health_check "Market Data Service" "$((base_port + 2))" "/health" || ((failed_checks++))
    detailed_health_check "Thread Service" "$((base_port + 3))" "/health/ready" || ((failed_checks++))
    
    # Summary
    if [[ $failed_checks -eq 0 ]]; then
        success "All health checks passed for $environment environment"
        return 0
    else
        error "$failed_checks health check(s) failed for $environment environment"
        return 1
    fi
}

# Performance monitoring
performance_check() {
    log "Checking system performance..."
    
    # Memory usage
    local memory_info=$(free -h | grep "Mem:")
    log "Memory: $memory_info"
    
    # Disk usage
    local disk_info=$(df -h / | tail -1)
    log "Disk: $disk_info"
    
    # Docker stats
    log "Docker container resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" 2>/dev/null || log "Docker stats unavailable"
    
    # Load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}')
    log "Load average:$load_avg"
}

# API endpoint testing
api_endpoint_test() {
    local base_url=${1:-"https://abraapp.undeclab.com"}
    
    log "Testing API endpoints at $base_url..."
    
    # Test main health endpoint
    if curl -f -s "$base_url/health" >/dev/null 2>&1; then
        success "Main health endpoint: OK"
    else
        error "Main health endpoint: Failed"
        return 1
    fi
    
    # Test API root
    local api_response=$(curl -s "$base_url/" 2>/dev/null)
    if echo "$api_response" | grep -q "Abra API Gateway"; then
        success "API root endpoint: OK"
    else
        error "API root endpoint: Failed"
        return 1
    fi
    
    # Test specific service endpoints (basic connectivity)
    local endpoints=("/api/auth/health" "/api/marketdata/health" "/api/assistant/health")
    
    for endpoint in "${endpoints[@]}"; do
        if curl -f -s "$base_url$endpoint" >/dev/null 2>&1; then
            success "Endpoint $endpoint: OK"
        else
            warning "Endpoint $endpoint: Not responding (may be expected)"
        fi
    done
    
    return 0
}

# Continuous monitoring mode
continuous_monitor() {
    local interval=${1:-60}  # Default 60 seconds
    local environment=$(get_current_environment)
    
    log "Starting continuous monitoring (interval: ${interval}s, environment: $environment)..."
    
    while true; do
        echo "----------------------------------------"
        system_health_check "$environment"
        performance_check
        echo "----------------------------------------"
        sleep "$interval"
    done
}

# Generate health report
generate_report() {
    local environment=$(get_current_environment)
    local report_file="health-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "Abra API Health Report"
        echo "Generated: $(date)"
        echo "Environment: $environment"
        echo "========================================"
        echo
        
        system_health_check "$environment"
        echo
        performance_check
        echo
        api_endpoint_test
        
    } | tee "$report_file"
    
    success "Health report generated: $report_file"
}

# Main script logic
case "${1:-check}" in
    "check")
        env=${2:-$(get_current_environment)}
        system_health_check "$env"
        ;;
    "monitor")
        interval=${2:-60}
        continuous_monitor "$interval"
        ;;
    "performance")
        performance_check
        ;;
    "api-test")
        base_url=${2:-"https://abraapp.undeclab.com"}
        api_endpoint_test "$base_url"
        ;;
    "report")
        generate_report
        ;;
    *)
        echo "Usage: $0 {check|monitor|performance|api-test|report} [options]"
        echo "  check [env]        - Perform health check (optionally specify environment)"
        echo "  monitor [interval] - Continuous monitoring (default 60s interval)"
        echo "  performance        - Check system performance metrics"
        echo "  api-test [url]     - Test API endpoints (default: https://abraapp.undeclab.com)"
        echo "  report             - Generate comprehensive health report"
        exit 1
        ;;
esac
