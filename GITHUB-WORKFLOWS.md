# GitHub Workflows for Abra API

This document explains the GitHub Actions workflows configured for automated deployment and SSL management.

## 🚀 Workflows Overview

### 1. Main Deployment Workflow (`ci-cd.yml`)
**Trigger**: Push to `main` branch or manual dispatch
**Purpose**: Build and deploy the application with optional SSL setup

### 2. SSL Certificate Renewal (`ssl-renewal.yml`)
**Trigger**: Daily at 2 AM UTC or manual dispatch
**Purpose**: Automatically renew SSL certificates when they expire within 30 days

### 3. Initial SSL Setup (`ssl-setup.yml`)
**Trigger**: Manual dispatch only
**Purpose**: First-time SSL certificate setup and configuration

## 🔧 Required GitHub Secrets

Add these secrets to your GitHub repository settings:

### Server Access
```
SSH_HOST=your-server-ip
SSH_USERNAME=your-username
SSH_PRIVATE_KEY=your-private-key
```

### Database & APIs
```
POSTGRES_USER=your-postgres-user
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_DB=your-database-name
REDIS_CONNECTION_STRING=redis:6379
FINNHUB_API_KEY=your-finnhub-key
POLYGON_API_KEY=your-polygon-key
ASPNETCORE_ENVIRONMENT=Production
```

### Supabase Configuration
```
SUPABASE_JWT_SECRET=your-jwt-secret
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_PROJECT_ID=your-project-id
SUPABASE_CONNECTION_STRING=your-connection-string
```

### Other Services
```
OLLAMA_URL=your-ollama-url
SSL_EMAIL=<EMAIL>
```

## 📋 Workflow Usage

### Initial SSL Setup

1. **First-time SSL setup:**
   ```
   Go to Actions → Initial SSL Setup → Run workflow
   Enter your email address
   Click "Run workflow"
   ```

2. **The workflow will:**
   - Update the repository
   - Configure SSL certificates with Let's Encrypt
   - Deploy with HTTPS enabled
   - Set up automatic renewal

### Regular Deployments

1. **Automatic deployment:**
   - Push to `main` branch
   - Workflow automatically detects if SSL is configured
   - Deploys with appropriate configuration (HTTP or HTTPS)

2. **Manual deployment with SSL setup:**
   ```
   Go to Actions → Build and Deploy with SSL to GCE → Run workflow
   Check "Setup SSL certificates" if needed
   Click "Run workflow"
   ```

### SSL Certificate Management

1. **Automatic renewal:**
   - Runs daily at 2 AM UTC
   - Only renews if certificate expires within 30 days
   - No action needed

2. **Manual renewal:**
   ```
   Go to Actions → SSL Certificate Renewal → Run workflow
   Check "Force certificate renewal" if needed
   Click "Run workflow"
   ```

## 🔍 Workflow Features

### Main Deployment (`ci-cd.yml`)
- ✅ Automatic SSL detection
- ✅ Smart Docker Compose file selection
- ✅ SSL certificate setup/renewal
- ✅ Health checks for HTTP and HTTPS
- ✅ Service status monitoring
- ✅ Comprehensive logging

### SSL Renewal (`ssl-renewal.yml`)
- ✅ Certificate expiry checking
- ✅ Automatic renewal (30 days before expiry)
- ✅ Force renewal option
- ✅ SSL configuration testing
- ✅ Failure notifications

### SSL Setup (`ssl-setup.yml`)
- ✅ First-time certificate generation
- ✅ Domain configuration
- ✅ Automatic cron job setup
- ✅ Comprehensive testing
- ✅ Service deployment

## 🛠️ Troubleshooting

### Common Issues

1. **SSL Setup Fails**
   - Check domain DNS points to server
   - Ensure port 80/443 are open
   - Verify email address is valid

2. **Certificate Renewal Fails**
   - Check disk space on server
   - Verify nginx is running
   - Check domain is still pointing to server

3. **Deployment Fails**
   - Check all required secrets are set
   - Verify server SSH access
   - Check Docker and Docker Compose versions

### Debugging Steps

1. **Check workflow logs:**
   - Go to Actions tab in GitHub
   - Click on failed workflow
   - Review step-by-step logs

2. **SSH to server manually:**
   ```bash
   ssh your-username@your-server-ip
   cd ~/abraapi
   docker compose ps
   docker compose logs
   ```

3. **Test SSL manually:**
   ```bash
   curl -I https://abraapp.undeclab.com/health
   openssl s_client -connect abraapp.undeclab.com:443
   ```

## 📊 Monitoring

### Health Checks
- HTTP: `http://abraapp.undeclab.com/health`
- HTTPS: `https://abraapp.undeclab.com/health`

### Certificate Status
```bash
# Check certificate expiry
openssl x509 -in certbot/conf/live/abraapp.undeclab.com/fullchain.pem -noout -dates

# Check certificate details
openssl x509 -in certbot/conf/live/abraapp.undeclab.com/fullchain.pem -noout -text
```

### Service Status
```bash
# Check all services
docker compose -f docker-compose.ssl.yml ps

# Check specific service logs
docker compose -f docker-compose.ssl.yml logs nginx
```

## 🔄 Workflow Customization

### Adding New Secrets
1. Go to repository Settings → Secrets and variables → Actions
2. Click "New repository secret"
3. Add name and value
4. Update workflow files to use the secret

### Modifying SSL Configuration
1. Edit `nginx/nginx.conf` for nginx settings
2. Edit `docker-compose.ssl.yml` for container configuration
3. Edit `setup-ssl.sh` for certificate management

### Changing Deployment Schedule
1. Edit `.github/workflows/ssl-renewal.yml`
2. Modify the `cron` expression in the `schedule` section
3. Commit changes to apply

## 📚 Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [nginx SSL Configuration](https://nginx.org/en/docs/http/configuring_https_servers.html)
