Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ThreadService", "ThreadService\ThreadService.csproj", "{F9134AB3-6D6E-CBE6-FE20-CB6064EB6AFF}"
EndProject

Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AuthService", "AuthService\AuthService.csproj", "{6CDAB7B3-DE4F-6FF5-06F0-D824F083F1AF}"
EndProject

Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MarketDataService", "MarketDataService\MarketDataService.csproj", "{E4866954-A0EA-13B1-B32C-F344BFF6AE39}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AssistantService", "AssistantService\AssistantService.csproj", "{F7485688-B565-6FD3-30A8-9A9EBF2F0E07}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "auth-service.Tests", "tests\auth-service.Tests\auth-service.Tests.csproj", "{7DB5961A-FD7E-D64E-E6E1-87D81D5AA08C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "assistant-service.Tests", "tests\assistant-service.Tests\assistant-service.Tests.csproj", "{1288872A-BDF1-4FE2-3DA2-8DBEFD107FBD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "marketdata-service.Tests", "tests\marketdata-service.Tests\marketdata-service.Tests.csproj", "{335B3EC4-18DB-38A2-70ED-704E61312893}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F9134AB3-6D6E-CBE6-FE20-CB6064EB6AFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9134AB3-6D6E-CBE6-FE20-CB6064EB6AFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9134AB3-6D6E-CBE6-FE20-CB6064EB6AFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9134AB3-6D6E-CBE6-FE20-CB6064EB6AFF}.Release|Any CPU.Build.0 = Release|Any CPU

		{6CDAB7B3-DE4F-6FF5-06F0-D824F083F1AF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CDAB7B3-DE4F-6FF5-06F0-D824F083F1AF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CDAB7B3-DE4F-6FF5-06F0-D824F083F1AF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CDAB7B3-DE4F-6FF5-06F0-D824F083F1AF}.Release|Any CPU.Build.0 = Release|Any CPU

		{E4866954-A0EA-13B1-B32C-F344BFF6AE39}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E4866954-A0EA-13B1-B32C-F344BFF6AE39}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E4866954-A0EA-13B1-B32C-F344BFF6AE39}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E4866954-A0EA-13B1-B32C-F344BFF6AE39}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7485688-B565-6FD3-30A8-9A9EBF2F0E07}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7485688-B565-6FD3-30A8-9A9EBF2F0E07}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7485688-B565-6FD3-30A8-9A9EBF2F0E07}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7485688-B565-6FD3-30A8-9A9EBF2F0E07}.Release|Any CPU.Build.0 = Release|Any CPU

		{7DB5961A-FD7E-D64E-E6E1-87D81D5AA08C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DB5961A-FD7E-D64E-E6E1-87D81D5AA08C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DB5961A-FD7E-D64E-E6E1-87D81D5AA08C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DB5961A-FD7E-D64E-E6E1-87D81D5AA08C}.Release|Any CPU.Build.0 = Release|Any CPU
		{1288872A-BDF1-4FE2-3DA2-8DBEFD107FBD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1288872A-BDF1-4FE2-3DA2-8DBEFD107FBD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1288872A-BDF1-4FE2-3DA2-8DBEFD107FBD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1288872A-BDF1-4FE2-3DA2-8DBEFD107FBD}.Release|Any CPU.Build.0 = Release|Any CPU
		{335B3EC4-18DB-38A2-70ED-704E61312893}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{335B3EC4-18DB-38A2-70ED-704E61312893}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{335B3EC4-18DB-38A2-70ED-704E61312893}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{335B3EC4-18DB-38A2-70ED-704E61312893}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7DB5961A-FD7E-D64E-E6E1-87D81D5AA08C} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{1288872A-BDF1-4FE2-3DA2-8DBEFD107FBD} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{335B3EC4-18DB-38A2-70ED-704E61312893} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {440B960C-DE61-43AA-8F08-749A6BDD5E7C}
	EndGlobalSection
EndGlobal
