#!/bin/bash

# Optimized Docker Build Script for Abra API
# This script builds all services with maximum optimization for speed and efficiency

set -e  # Exit on any error

echo "🚀 Starting optimized Docker build process..."

# Enable Docker BuildKit for better performance
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker BuildKit is available
if ! docker buildx version >/dev/null 2>&1; then
    print_warning "Docker BuildKit not available, using standard build"
    export DOCKER_BUILDKIT=0
else
    print_status "Docker BuildKit enabled for optimized builds"
fi

# Function to build individual service with optimizations
build_service() {
    local service_name=$1
    local context_path=$2
    
    print_status "Building $service_name..."
    
    # Build with cache optimization
    docker build \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --cache-from $service_name:latest \
        --tag $service_name:latest \
        --target runtime \
        $context_path
    
    if [ $? -eq 0 ]; then
        print_success "$service_name built successfully"
    else
        print_error "Failed to build $service_name"
        exit 1
    fi
}

# Function to build all services in parallel
build_parallel() {
    print_status "Building services in parallel for maximum speed..."
    
    # Build services in parallel using background processes
    build_service "abraapi-assistant" "./AssistantService" &
    PID1=$!
    
    build_service "abraapi-auth" "./AuthService" &
    PID2=$!
    
    build_service "abraapi-marketdata" "./MarketDataService" &
    PID3=$!
    
    build_service "abraapi-thread" "./ThreadService" &
    PID4=$!
    
    # Wait for all builds to complete
    wait $PID1 && print_success "AssistantService build completed"
    wait $PID2 && print_success "AuthService build completed"
    wait $PID3 && print_success "MarketDataService build completed"
    wait $PID4 && print_success "ThreadService build completed"
}

# Function to build using docker-compose (recommended)
build_compose() {
    print_status "Building with docker-compose for orchestrated build..."
    
    # Use docker-compose for optimized parallel builds
    docker-compose build \
        --parallel \
        --compress \
        --force-rm \
        --no-cache
    
    if [ $? -eq 0 ]; then
        print_success "All services built successfully with docker-compose"
    else
        print_error "Docker-compose build failed"
        exit 1
    fi
}

# Function to clean up build artifacts
cleanup() {
    print_status "Cleaning up build artifacts..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove build cache (optional - comment out to keep cache)
    # docker builder prune -f
    
    print_success "Cleanup completed"
}

# Function to show build statistics
show_stats() {
    print_status "Build Statistics:"
    echo "Docker images:"
    docker images | grep -E "(abraapi|assistant-service|auth-service|marketdata-service|thread-service)"
    
    echo ""
    echo "Docker system usage:"
    docker system df
}

# Main execution
main() {
    local build_method=${1:-compose}
    
    case $build_method in
        "parallel")
            build_parallel
            ;;
        "compose")
            build_compose
            ;;
        "individual")
            build_service "abraapi-assistant" "./AssistantService"
            build_service "abraapi-auth" "./AuthService"
            build_service "abraapi-marketdata" "./MarketDataService"
            build_service "abraapi-thread" "./ThreadService"
            ;;
        *)
            print_error "Invalid build method. Use: parallel, compose, or individual"
            exit 1
            ;;
    esac
    
    cleanup
    show_stats
    
    print_success "🎉 Optimized build process completed!"
    print_status "You can now run: docker-compose up -d"
}

# Help function
show_help() {
    echo "Usage: $0 [build_method]"
    echo ""
    echo "Build methods:"
    echo "  compose     - Use docker-compose build (default, recommended)"
    echo "  parallel    - Build services in parallel manually"
    echo "  individual  - Build services one by one"
    echo ""
    echo "Examples:"
    echo "  $0                    # Use docker-compose build"
    echo "  $0 compose           # Use docker-compose build"
    echo "  $0 parallel          # Build in parallel manually"
    echo "  $0 individual        # Build one by one"
    echo ""
    echo "Environment variables:"
    echo "  DOCKER_BUILDKIT=1    # Enable BuildKit (automatically set)"
    echo "  COMPOSE_DOCKER_CLI_BUILD=1  # Enable BuildKit for compose"
}

# Check for help flag
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# Run main function
main "$@"