#!/bin/bash

# Setup script for zero-downtime deployment system
# This script initializes the blue-green deployment environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   error "This script should not be run as root"
   exit 1
fi

log "Setting up zero-downtime deployment system..."

# Make scripts executable
log "Making deployment scripts executable..."
chmod +x deploy-zero-downtime.sh
chmod +x health-monitor.sh
chmod +x rollback-recovery.sh
chmod +x setup-ssl.sh 2>/dev/null || true

# Create required directories
log "Creating required directories..."
mkdir -p nginx
mkdir -p certbot/www
mkdir -p certbot/conf
mkdir -p backups

# Initialize environment tracking
if [[ ! -f ".current_environment" ]]; then
    log "Initializing environment tracking..."
    echo "blue" > .current_environment
    success "Default environment set to blue"
else
    current_env=$(cat .current_environment)
    log "Current environment: $current_env"
fi

# Set up nginx upstream configuration
if [[ ! -f "nginx/upstreams.conf" ]]; then
    log "Setting up nginx upstream configuration..."
    current_env=$(cat .current_environment)
    if [[ -f "nginx/upstreams.$current_env.conf" ]]; then
        cp "nginx/upstreams.$current_env.conf" "nginx/upstreams.conf"
        success "Nginx upstream configuration initialized for $current_env environment"
    else
        warning "Upstream configuration file not found for $current_env environment"
    fi
fi

# Check Docker and Docker Compose
log "Checking Docker installation..."
if command -v docker &> /dev/null; then
    success "Docker is installed: $(docker --version)"
else
    error "Docker is not installed. Please install Docker first."
    exit 1
fi

if command -v docker-compose &> /dev/null || docker compose version &> /dev/null; then
    success "Docker Compose is available"
else
    error "Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    error "Docker daemon is not running. Please start Docker."
    exit 1
fi

# Create Docker network if it doesn't exist
log "Setting up Docker network..."
if ! docker network ls | grep -q "abraapi-network"; then
    docker network create abraapi-network
    success "Created Docker network: abraapi-network"
else
    log "Docker network already exists: abraapi-network"
fi

# Validate configuration files
log "Validating configuration files..."

required_files=(
    "docker-compose.blue.yml"
    "docker-compose.green.yml"
    "nginx/nginx.blue-green.conf"
    "nginx/upstreams.blue.conf"
    "nginx/upstreams.green.conf"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        missing_files+=("$file")
    fi
done

if [[ ${#missing_files[@]} -gt 0 ]]; then
    error "Missing required files:"
    for file in "${missing_files[@]}"; do
        echo "  - $file"
    done
    exit 1
else
    success "All required configuration files are present"
fi

# Test nginx configuration
log "Testing nginx configuration..."
if docker run --rm -v "$(pwd)/nginx/nginx.blue-green.conf:/etc/nginx/nginx.conf:ro" -v "$(pwd)/nginx/upstreams.conf:/etc/nginx/upstreams.conf:ro" nginx:alpine nginx -t; then
    success "Nginx configuration is valid"
else
    error "Nginx configuration is invalid"
    exit 1
fi

# Check environment variables
log "Checking environment configuration..."
if [[ -f ".env" ]]; then
    success "Environment file (.env) exists"
    
    # Check for critical environment variables
    critical_vars=("POSTGRES_USER" "POSTGRES_PASSWORD" "POSTGRES_DB" "SUPABASE_CONNECTION_STRING")
    missing_vars=()
    
    for var in "${critical_vars[@]}"; do
        if ! grep -q "^$var=" .env; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        warning "Missing critical environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        warning "Please ensure all required environment variables are set"
    else
        success "All critical environment variables are configured"
    fi
else
    warning "Environment file (.env) not found"
    warning "Please create .env file with required environment variables"
fi

# Initialize deployment history
if [[ ! -f ".deployment_history" ]]; then
    log "Initializing deployment history..."
    echo "timestamp,environment,status,commit" > .deployment_history
    success "Deployment history initialized"
fi

# Test basic functionality
log "Testing basic functionality..."

# Test health monitor
if ./health-monitor.sh check blue 2>/dev/null; then
    success "Health monitor is working"
else
    log "Health monitor test completed (services may not be running yet)"
fi

# Final setup summary
echo
success "Zero-downtime deployment system setup completed!"
echo
log "Next steps:"
echo "  1. Ensure your .env file contains all required environment variables"
echo "  2. Start your first deployment: ./deploy-zero-downtime.sh deploy"
echo "  3. Monitor deployment: ./health-monitor.sh monitor"
echo "  4. For SSL setup, run the deployment with SSL options in GitHub Actions"
echo
log "Available commands:"
echo "  - Deploy: ./deploy-zero-downtime.sh deploy"
echo "  - Status: ./deploy-zero-downtime.sh status"
echo "  - Health: ./health-monitor.sh check"
echo "  - Rollback: ./rollback-recovery.sh rollback"
echo "  - Monitor: ./health-monitor.sh monitor"
echo
log "For detailed documentation, see: ZERO_DOWNTIME_DEPLOYMENT.md"
