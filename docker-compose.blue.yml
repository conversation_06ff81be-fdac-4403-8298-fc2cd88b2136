version: '3.8'

services:
  # Database services - shared between blue/green
  postgres:
    image: postgres:15-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - pgdata_market:/var/lib/postgresql/data
    networks:
      - abraapi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - abraapi-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Blue environment services
  assistant-service-blue:
    container_name: assistant-service-blue
    restart: unless-stopped
    build:
      context: ./AssistantService
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "8081:8080"  # Blue environment port
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - OLLAMA_URL=${OLLAMA_URL:-http://assistant.undeclab.com/api/generate}
    networks:
      - abraapi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  auth-service-blue:
    container_name: auth-service-blue
    restart: unless-stopped
    build:
      context: ./AuthService
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "8082:8080"  # Blue environment port
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_PROJECT_ID=${SUPABASE_PROJECT_ID}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    networks:
      - abraapi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  marketdata-service-blue:
    container_name: marketdata-service-blue
    restart: unless-stopped
    build:
      context: ./MarketDataService
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "8083:8080"  # Blue environment port
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - SUPABASE_CONNECTION_STRING=${SUPABASE_CONNECTION_STRING}
      - Redis__ConnectionString=${REDIS_CONNECTION_STRING}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - abraapi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  thread-service-blue:
    container_name: thread-service-blue
    restart: unless-stopped
    build:
      context: ./ThreadService
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "8084:8080"  # Blue environment port
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - SUPABASE_CONNECTION_STRING=${SUPABASE_CONNECTION_STRING}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
    networks:
      - abraapi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health/ready"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s

networks:
  abraapi-network:
    driver: bridge

volumes:
  pgdata_market:
  redis_data:
